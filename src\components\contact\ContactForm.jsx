import { useState } from 'react';
import styled from 'styled-components';
import { colors, spacing, typography } from '../../styles';
import { useTheme } from '../../context/ThemeContext';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPaperPlane, FaCheck, FaTimes } from 'react-icons/fa';

// Add constants for field limits
const MAX_MESSAGE_LENGTH = 1500;
const MAX_NAME_LENGTH = 50;
const MAX_EMAIL_LENGTH = 100;
const MAX_SUBJECT_LENGTH = 100;

const FormContainer = styled(motion.div)`
  padding: ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  border-radius: 8px;
  margin-bottom: ${spacing.lg};
  overflow: hidden;
`;

const Title = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const Input = styled.input`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.darkGray : colors.neutral.lightGray};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.black : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const TextArea = styled.textarea`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.darkGray : colors.neutral.lightGray};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.black : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  min-height: 150px;
  resize: vertical;
`;

const Button = styled(motion.button)`
  padding: ${spacing.sm} ${spacing.md};
  background-color: ${colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${spacing.xs};
  
  &:hover {
    background-color: ${colors.primary.dark};
  }
  
  &:disabled {
    background-color: ${colors.neutral.gray};
    cursor: not-allowed;
  }
`;

const Message = styled(motion.div)`
  padding: ${spacing.sm};
  border-radius: 4px;
  margin-top: ${spacing.md};
  background-color: ${props => props.type === 'success' 
    ? colors.success.light 
    : colors.error.light};
  color: ${props => props.type === 'success' 
    ? colors.success.dark 
    : colors.error.dark};
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
`;

const InputWrapper = styled(motion.div)`
  width: 100%;
`;

const CharCounter = styled.div`
  font-size: 0.8rem;
  text-align: right;
  margin-top: 4px;
  opacity: ${props => props.show === false ? 0 : 1};
  color: ${props => 
    props.count > (props.max * 0.9) 
      ? colors.error.main 
      : props.darkMode 
        ? colors.neutral.light 
        : colors.neutral.gray
  };
  transition: opacity 0.3s ease;
`;

const ContactForm = () => {
  const { darkMode } = useTheme();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState({ message: '', type: '' });
  const [charCount, setCharCount] = useState({
    name: 0,
    email: 0,
    subject: 0,
    message: 0
  });
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Apply character limits to all fields
    let maxLength;
    switch(name) {
      case 'name':
        maxLength = MAX_NAME_LENGTH;
        break;
      case 'email':
        maxLength = MAX_EMAIL_LENGTH;
        break;
      case 'subject':
        maxLength = MAX_SUBJECT_LENGTH;
        break;
      case 'message':
        maxLength = MAX_MESSAGE_LENGTH;
        break;
      default:
        maxLength = Infinity;
    }
    
    if (value.length <= maxLength) {
      setFormData(prev => ({ ...prev, [name]: value }));
      setCharCount(prev => ({ ...prev, [name]: value.length }));
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setStatus({ message: '', type: '' });
    
    try {
      await addDoc(collection(db, 'messages'), {
        ...formData,
        date: new Date().toISOString(),
        isRead: false,
        isStarred: false
      });
      
      setStatus({ 
        message: 'Your message has been sent successfully! We will get back to you soon.', 
        type: 'success' 
      });
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      console.error('Error sending message:', error);
      setStatus({ 
        message: 'Failed to send your message. Please try again later.', 
        type: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };
  
  return (
    <FormContainer 
      darkMode={darkMode}
      initial="hidden"
      animate="visible"
      variants={formVariants}
    >
      <Title darkMode={darkMode}>Contact Us</Title>
      <Form onSubmit={handleSubmit}>
        <InputWrapper variants={itemVariants}>
          <FormGroup>
            <Label darkMode={darkMode}>Name</Label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              darkMode={darkMode}
              maxLength={MAX_NAME_LENGTH}
            />
            <CharCounter 
              darkMode={darkMode} 
              count={charCount.name}
              show={charCount.name > MAX_NAME_LENGTH * 0.7}
            >
              {charCount.name}/{MAX_NAME_LENGTH}
            </CharCounter>
          </FormGroup>
        </InputWrapper>
        
        <InputWrapper variants={itemVariants}>
          <FormGroup>
            <Label darkMode={darkMode}>Email</Label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              darkMode={darkMode}
              maxLength={MAX_EMAIL_LENGTH}
            />
            <CharCounter 
              darkMode={darkMode} 
              count={charCount.email}
              show={charCount.email > MAX_EMAIL_LENGTH * 0.7}
            >
              {charCount.email}/{MAX_EMAIL_LENGTH}
            </CharCounter>
          </FormGroup>
        </InputWrapper>
        
        <InputWrapper variants={itemVariants}>
          <FormGroup>
            <Label darkMode={darkMode}>Subject</Label>
            <Input
              type="text"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              required
              darkMode={darkMode}
              maxLength={MAX_SUBJECT_LENGTH}
            />
            <CharCounter 
              darkMode={darkMode} 
              count={charCount.subject}
              show={charCount.subject > MAX_SUBJECT_LENGTH * 0.7}
            >
              {charCount.subject}/{MAX_SUBJECT_LENGTH}
            </CharCounter>
          </FormGroup>
        </InputWrapper>
        
        <InputWrapper variants={itemVariants}>
          <FormGroup>
            <Label darkMode={darkMode}>Message</Label>
            <TextArea
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
              darkMode={darkMode}
              maxLength={MAX_MESSAGE_LENGTH}
            />
            <CharCounter 
              darkMode={darkMode} 
              count={charCount.message}
            >
              {charCount.message}/{MAX_MESSAGE_LENGTH} characters
            </CharCounter>
          </FormGroup>
        </InputWrapper>
        
        <motion.div variants={itemVariants}>
          <Button 
            type="submit" 
            disabled={loading}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {loading ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                >
                  <FaPaperPlane />
                </motion.div>
                Sending...
              </>
            ) : (
              <>
                <FaPaperPlane /> Send Message
              </>
            )}
          </Button>
        </motion.div>
      </Form>
      
      <AnimatePresence>
        {status.message && (
          <Message 
            type={status.type}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {status.type === 'success' ? <FaCheck /> : <FaTimes />}
            {status.message}
          </Message>
        )}
      </AnimatePresence>
    </FormContainer>
  );
};

export default ContactForm;



