import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FaKeyboard, FaTimes } from 'react-icons/fa';
import { colors, spacing, typography } from '../../../styles';

const HelpButton = styled(motion.button)`
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  cursor: pointer;
  padding: ${spacing.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
`;

const HelpPanel = styled(motion.div)`
  position: fixed;
  bottom: 70px;
  right: 20px;
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  padding: ${spacing.md};
  width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 100;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};
`;

const Title = styled.h3`
  margin: 0;
  font-family: ${typography.fontFamily.secondary};
  font-weight: ${typography.fontWeight.medium};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ShortcutList = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  gap: ${spacing.sm} ${spacing.md};
  align-items: center;
`;

const KeyboardKey = styled.span`
  display: inline-block;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  border: 1px solid ${props => props.darkMode ? colors.neutral.main : colors.neutral.light};
  border-radius: 4px;
  padding: 2px 8px;
  font-family: monospace;
  font-weight: bold;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
`;

const ShortcutDescription = styled.span`
  font-size: 0.9rem;
`;

const KeyboardShortcutsHelp = ({ darkMode }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const toggleHelp = () => setIsOpen(!isOpen);
  
  const shortcuts = [
    { key: 'R', description: 'Mark as read' },
    { key: 'U', description: 'Mark as unread' },
    { key: 'S', description: 'Toggle star' },
    { key: 'A', description: 'Toggle archive' },
    { key: 'D', description: 'Delete (with confirmation)' }
  ];
  
  return (
    <>
      <HelpButton 
        darkMode={darkMode}
        onClick={toggleHelp}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Keyboard Shortcuts"
      >
        <FaKeyboard />
      </HelpButton>
      
      <AnimatePresence>
        {isOpen && (
          <HelpPanel
            darkMode={darkMode}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
          >
            <PanelHeader>
              <Title>Keyboard Shortcuts</Title>
              <CloseButton 
                darkMode={darkMode} 
                onClick={toggleHelp}
                aria-label="Close"
              >
                <FaTimes />
              </CloseButton>
            </PanelHeader>
            
            <ShortcutList>
              {shortcuts.map(shortcut => (
                <React.Fragment key={shortcut.key}>
                  <KeyboardKey darkMode={darkMode}>{shortcut.key}</KeyboardKey>
                  <ShortcutDescription>{shortcut.description}</ShortcutDescription>
                </React.Fragment>
              ))}
            </ShortcutList>
          </HelpPanel>
        )}
      </AnimatePresence>
    </>
  );
};

export default KeyboardShortcutsHelp;