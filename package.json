{"name": "skin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "setup-admin": "node scripts/setup-admin.mjs"}, "dependencies": {"@paypal/react-paypal-js": "^8.8.2", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "algoliasearch": "^4.14.3", "dotenv": "^16.3.1", "firebase": "^10.8.0", "framer-motion": "^12.5.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^6.22.0", "styled-components": "^6.1.16"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}