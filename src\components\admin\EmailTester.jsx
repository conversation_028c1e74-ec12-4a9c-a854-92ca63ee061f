import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FaPaperPlane, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { colors, spacing } from '../../styles';
import { useAuth } from '../../context/AuthContext';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../firebase/config';

const Container = styled.div`
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  margin: ${spacing.lg} 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h3`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.md};
  font-size: 1.25rem;
`;

const Form = styled.form`
  display: grid;
  gap: ${spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-weight: 500;
  margin-bottom: ${spacing.sm};
`;

const Input = styled.input`
  padding: ${spacing.md};
  border: 2px solid ${props => props.darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 8px;
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const TextArea = styled.textarea`
  padding: ${spacing.md};
  border: 2px solid ${props => props.darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 8px;
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const Button = styled(motion.button)`
  background: linear-gradient(135deg, ${colors.primary.main}, ${colors.primary.dark});
  color: white;
  border: none;
  padding: ${spacing.md} ${spacing.lg};
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  justify-self: start;
  
  &:disabled {
    background: ${colors.neutral.light};
    color: ${colors.neutral.dark};
    cursor: not-allowed;
  }
`;

const StatusMessage = styled(motion.div)`
  padding: ${spacing.md};
  border-radius: 8px;
  margin-top: ${spacing.md};
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  
  ${props => props.type === 'success' ? `
    background: ${colors.success.light}20;
    color: ${colors.success.dark};
    border: 1px solid ${colors.success.light};
  ` : `
    background: ${colors.error.light}20;
    color: ${colors.error.dark};
    border: 1px solid ${colors.error.light};
  `}
`;

const EmailTester = ({ darkMode }) => {
  const [formData, setFormData] = useState({
    to: '',
    subject: '',
    message: '',
    orderReference: ''
  });
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState({ message: '', type: '' });
  const { user } = useAuth();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setStatus({ message: '', type: '' });

    try {
      const sendCustomEmail = httpsCallable(functions, 'sendCustomEmail');
      const result = await sendCustomEmail(formData);
      
      setStatus({
        message: result.data.message,
        type: 'success'
      });
      
      // Reset form after successful send
      setFormData({ to: '', subject: '', message: '', orderReference: '' });
      
    } catch (error) {
      console.error('Error sending email:', error);
      setStatus({
        message: error.message || 'Failed to send email. Please try again.',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container darkMode={darkMode}>
      <Title darkMode={darkMode}>🧪 Test Email System</Title>
      
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label darkMode={darkMode}>To Email Address</Label>
          <Input
            type="email"
            name="to"
            value={formData.to}
            onChange={handleChange}
            required
            darkMode={darkMode}
            placeholder="<EMAIL>"
          />
        </FormGroup>

        <FormGroup>
          <Label darkMode={darkMode}>Subject</Label>
          <Input
            type="text"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
            required
            darkMode={darkMode}
            placeholder="Test Email Subject"
          />
        </FormGroup>

        <FormGroup>
          <Label darkMode={darkMode}>Order Reference (optional)</Label>
          <Input
            type="text"
            name="orderReference"
            value={formData.orderReference}
            onChange={handleChange}
            darkMode={darkMode}
            placeholder="ORDER_123"
          />
        </FormGroup>

        <FormGroup>
          <Label darkMode={darkMode}>Message</Label>
          <TextArea
            name="message"
            value={formData.message}
            onChange={handleChange}
            required
            darkMode={darkMode}
            placeholder="Your test message here..."
          />
        </FormGroup>

        <Button
          type="submit"
          disabled={loading}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {loading ? (
            <>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
              >
                <FaPaperPlane />
              </motion.div>
              Testing...
            </>
          ) : (
            <>
              <FaPaperPlane /> Test Email System
            </>
          )}
        </Button>
      </Form>

      {status.message && (
        <StatusMessage
          type={status.type}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          {status.type === 'success' ? <FaCheck /> : <FaExclamationTriangle />}
          {status.message}
        </StatusMessage>
      )}
    </Container>
  );
};

export default EmailTester;
