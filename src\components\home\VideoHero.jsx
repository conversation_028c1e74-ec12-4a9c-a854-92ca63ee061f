import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { Button } from '../ui/Button';
import { Link } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
// Import the video file
import heroVideo from '../../assets/videos/video.mp4';

const HeroContainer = styled.section`
  position: relative;
  height: 80vh;
  min-height: 500px;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: ${breakpoints.md}) {
    height: 60vh;
  }
`;

const VideoBackground = styled.video`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
`;

const FallbackImage = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${props => props.src});
  background-size: cover;
  background-position: center;
  z-index: 1;
`;

const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    rgba(0, 0, 0, 0.3),
    rgba(0, 0, 0, 0.4)
  );
  z-index: 2;
`;

const ContentContainer = styled(motion.div)`
  position: relative;
  z-index: 3;
  text-align: center;
  max-width: 800px;
  padding: ${spacing.xl};
`;

const HeroTitle = styled(motion.h1)`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 3.5rem;
  margin-bottom: ${spacing.md};
  font-weight: 700;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
  
  @media (max-width: ${breakpoints.md}) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled(motion.p)`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: ${typography.fontSize.xl};
  margin-bottom: ${spacing.xl};
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
  
  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.lg};
  }
`;

const ButtonContainer = styled(motion.div)`
  display: flex;
  gap: ${spacing.md};
  justify-content: center;
  
  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
    align-items: center;
  }
`;

const VideoHero = ({ 
  videoSrc = heroVideo, // Use the imported video as default
  fallbackImageSrc = "/images/skincare-hero-fallback.jpg",
  title = "Natural Beauty, Naturally You",
  subtitle = "Discover our handcrafted skincare products made with premium natural ingredients for radiant, healthy skin."
}) => {
  const [videoLoaded, setVideoLoaded] = useState(false);
  const { darkMode } = useTheme();
  
  useEffect(() => {
    // Preload the video
    const video = document.createElement('video');
    video.src = videoSrc;
    video.oncanplaythrough = () => setVideoLoaded(true);
    video.load();
    
    return () => {
      video.oncanplaythrough = null;
    };
  }, [videoSrc]);

  return (
    <HeroContainer>
      {videoLoaded ? (
        <VideoBackground autoPlay muted loop playsInline>
          <source src={videoSrc} type="video/mp4" />
        </VideoBackground>
      ) : (
        <FallbackImage src={fallbackImageSrc} />
      )}
      <Overlay />
      <ContentContainer
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <HeroTitle
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {title}
        </HeroTitle>
        <HeroSubtitle
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {subtitle}
        </HeroSubtitle>
        <ButtonContainer
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Button 
            as={Link} 
            to="/shop" 
            size="lg" 
            variant="primary" 
            darkMode={darkMode}
            style={{ color: darkMode ? colors.neutral.white : colors.neutral.darkest }}
          >
            Shop Now
          </Button>
          <Button 
            as={Link} 
            to="/about" 
            size="lg" 
            variant="secondary" 
            darkMode={darkMode}
            style={{ color: darkMode ? colors.neutral.white : colors.neutral.darkest }}
          >
            Our Story
          </Button>
        </ButtonContainer>
      </ContentContainer>
    </HeroContainer>
  );
};

export default VideoHero;





