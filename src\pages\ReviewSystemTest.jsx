import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { colors, spacing, typography } from '../styles';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { testReviewSystem } from '../utils/testReviews';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${spacing.xl};
`;

const Title = styled.h1`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.lg};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const Section = styled.div`
  margin-bottom: ${spacing.xl};
  padding: ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const Message = styled.div`
  padding: ${spacing.sm};
  margin-bottom: ${spacing.md};
  border-radius: 4px;
  background-color: ${props => 
    props.type === 'success' ? colors.success : 
    props.type === 'error' ? colors.error : 
    props.type === 'warning' ? colors.warning : 
    colors.info};
  color: white;
`;

const Button = styled.button`
  background-color: ${colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.sm} ${spacing.md};
  cursor: pointer;
  margin-right: ${spacing.sm};
  
  &:hover {
    background-color: ${colors.primary.dark};
  }
  
  &:disabled {
    background-color: ${colors.neutral.medium};
    cursor: not-allowed;
  }
`;

const Input = styled.input`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  margin-right: ${spacing.sm};
`;

const ProductList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${spacing.md};
`;

const ProductCard = styled.div`
  padding: ${spacing.md};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 8px;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  cursor: pointer;
  
  &:hover {
    border-color: ${colors.primary.main};
  }
`;

const TestResults = styled.pre`
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
  padding: ${spacing.md};
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const ReviewSystemTest = () => {
  const { darkMode } = useTheme();
  const { user, isAuthenticated } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [selectedProductId, setSelectedProductId] = useState('');
  const [testResults, setTestResults] = useState(null);
  const [testRunning, setTestRunning] = useState(false);
  
  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const productsRef = collection(db, 'products');
        const q = query(productsRef, orderBy('name'));
        const snapshot = await getDocs(q);
        const productsList = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setProducts(productsList);
      } catch (error) {
        console.error('Error fetching products:', error);
        setMessage({ text: `Error fetching products: ${error.message}`, type: 'error' });
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, []);
  
  // Handle running the test
  const handleRunTest = async () => {
    if (!selectedProductId) {
      setMessage({ text: 'Please select a product first', type: 'error' });
      return;
    }
    
    setTestRunning(true);
    setMessage({ text: 'Running review system test...', type: 'info' });
    
    try {
      const results = await testReviewSystem(selectedProductId);
      setTestResults(results);
      
      if (results.success) {
        setMessage({ text: 'Test completed successfully!', type: 'success' });
      } else {
        setMessage({ text: `Test failed: ${results.error}`, type: 'error' });
      }
    } catch (error) {
      console.error('Error running test:', error);
      setMessage({ text: `Error running test: ${error.message}`, type: 'error' });
    } finally {
      setTestRunning(false);
    }
  };
  
  return (
    <Container>
      <Title darkMode={darkMode}>Review System Test</Title>
      
      {!isAuthenticated && (
        <Message type="error">
          You must be logged in to run tests. Please log in first.
        </Message>
      )}
      
      {message.text && (
        <Message type={message.type}>{message.text}</Message>
      )}
      
      <Section darkMode={darkMode}>
        <SectionTitle darkMode={darkMode}>Run Review System Test</SectionTitle>
        
        <div>
          <label htmlFor="product-select">Select a product: </label>
          <Input
            type="text"
            id="product-select"
            list="products-list"
            placeholder="Type or select a product"
            value={selectedProductId}
            onChange={(e) => setSelectedProductId(e.target.value)}
            darkMode={darkMode}
          />
          <datalist id="products-list">
            {products.map(product => (
              <option key={product.id} value={product.id}>
                {product.name}
              </option>
            ))}
          </datalist>
        </div>
        
        <div style={{ marginTop: spacing.md }}>
          <Button 
            onClick={handleRunTest}
            disabled={!isAuthenticated || !selectedProductId || testRunning}
          >
            {testRunning ? 'Running Test...' : 'Run Test'}
          </Button>
        </div>
        
        {testResults && (
          <div style={{ marginTop: spacing.md }}>
            <SectionTitle darkMode={darkMode}>Test Results</SectionTitle>
            <TestResults darkMode={darkMode}>
              {JSON.stringify(testResults, null, 2)}
            </TestResults>
          </div>
        )}
      </Section>
      
      <Section darkMode={darkMode}>
        <SectionTitle darkMode={darkMode}>Available Products</SectionTitle>
        
        {loading ? (
          <p>Loading products...</p>
        ) : (
          <ProductList>
            {products.map(product => (
              <ProductCard key={product.id} darkMode={darkMode} onClick={() => setSelectedProductId(product.id)}>
                <h3>{product.name}</h3>
                <p>ID: {product.id}</p>
                <p>Category: {product.category}</p>
                <p>Price: ${product.price.toFixed(2)}</p>
              </ProductCard>
            ))}
          </ProductList>
        )}
      </Section>
    </Container>
  );
};

export default ReviewSystemTest;
