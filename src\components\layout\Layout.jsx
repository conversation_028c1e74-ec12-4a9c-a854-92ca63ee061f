import { useTheme } from '../../context/ThemeContext';
import Header from './Header';
import Footer from './Footer';
import styled from 'styled-components';
import Banner from '../home/<USER>';
import { useLocation } from 'react-router-dom';

const Main = styled.main`
  min-height: calc(100vh - 200px);
  padding: 20px;
  background-color: ${props => props.darkmode ? '#121212' : '#ffffff'};
  transition: background-color 0.3s ease;
  position: relative;
  z-index: 1;
`;

const Layout = ({ children }) => {
  const { darkMode } = useTheme();
  const location = useLocation();
  const isHomePage = location.pathname === '/';
  
  return (
    <>
      <Header />
      {isHomePage && <Banner />}
      <Main darkmode={darkMode}>
        {children}
      </Main>
      <Footer />
    </>
  );
};

export default Layout;




