import { useState, useEffect } from 'react';
import { collection, getDocs, doc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage, auth } from '../../firebase/config';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { uploadImage } from '../../utils/fileUpload';
import DeleteButton from '../ui/DeleteButton';
import { useAuth } from '../../context/AuthContext';
import DeleteConfirmationModal from '../ui/DeleteConfirmationModal';

const Container = styled.div`
  padding: ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  border-radius: 8px;
  margin-bottom: ${spacing.lg};
  
  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.md};
  }
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.sm};
  }
`;

const Title = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const BlogGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  
  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const BlogCard = styled.div`
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  padding: ${spacing.md};
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const BlogImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: ${spacing.sm};
  
  @media (max-width: ${breakpoints.sm}) {
    height: 120px;
  }
`;

const Button = styled.button`
  background-color: ${props => props.danger ? colors.error : props.primary ? colors.primary.main : colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.sm} ${spacing.md};
  margin-right: ${spacing.sm};
  cursor: pointer;
  max-width: 200px;
  
  &:hover {
    background-color: ${props => props.danger ? colors.errorDark : props.primary ? colors.primary.dark : colors.secondary.dark};
  }
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs} ${spacing.sm};
    margin-right: 0;
    margin-bottom: ${spacing.xs};
    max-width: 100%;
  }
`;

const Form = styled.form`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  padding: ${spacing.md};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  
  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.sm};
    gap: ${spacing.sm};
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Input = styled.input`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  width: 100%;
  box-sizing: border-box;
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs};
  }
`;

const TextArea = styled.textarea`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  min-height: 200px;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  width: 100%;
  box-sizing: border-box;
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs};
    min-height: 150px;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${spacing.sm};
  margin-top: ${spacing.sm};
  
  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
    width: 100%;
  }
`;

const Message = styled.div`
  padding: ${spacing.sm};
  margin-bottom: ${spacing.md};
  border-radius: 4px;
  background-color: ${props => props.type === 'success' ? colors.success : colors.error};
  color: white;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
  
  option {
    background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  }
`;

const CategorySelect = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.md};
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const CategoryTag = styled.span`
  display: inline-block;
  background: ${props => props.main 
    ? (props.darkMode ? colors.primary.dark : colors.primary.main)
    : (props.darkMode ? colors.primary.darker : colors.primary.light)};
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 5px;
  margin-bottom: 5px;
`;

const FileInput = styled.div`
  position: relative;
  
  input[type="file"] {
    font-size: 0.9rem;
    width: 100%;
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
    
    &::-webkit-file-upload-button {
      background-color: ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
      color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
      padding: ${spacing.xs} ${spacing.sm};
      border: none;
      border-radius: 4px;
      margin-right: ${spacing.sm};
      cursor: pointer;
    }
    
    &::file-selector-button {
      background-color: ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
      color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
      padding: ${spacing.xs} ${spacing.sm};
      border: none;
      border-radius: 4px;
      margin-right: ${spacing.sm};
      cursor: pointer;
    }
  }
`;

const BlogManager = ({ darkMode }) => {
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [showForm, setShowForm] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    summary: '',
    content: '',
    author: '',
    image: null,
    featured: false,
    category: '',
    subcategory: ''
  });
  // Add pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage, setPostsPerPage] = useState(10); // Show 10 posts per page in admin view
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    post: null
  });
  
  // Add resize listener
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Adjust posts per page based on screen size
      if (window.innerWidth < parseInt(breakpoints.md)) {
        setPostsPerPage(5); // 5 posts on smaller screens
      } else {
        setPostsPerPage(10); // 10 posts on larger screens
      }
    };
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Set initial value
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Add pagination calculation
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = blogPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(blogPosts.length / postsPerPage);
  
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const categoryStructure = {
    skincare: ['Cleansers', 'Moisturizers', 'Serums', 'Masks', 'Sunscreen', 'Treatments'],
    makeup: ['Foundation', 'Foundation','Concealer', 'Eyeshadow', 'Lipstick'],
    wellness: ['Meditation', 'Yoga', 'Nutrition', 'Fitness'],
    haircare: ['Shampoo', 'Conditioner', 'Styling Products'],
    bodycare: ['Bath Bombs', 'Body Wash', 'Exfoliants'],
    tips: ['DIY Projects', 'Product Reviews', 'Beauty Hacks'],
    ingredients: ['Active Ingredients', 'Natural Ingredients', 'Chemical Ingredients'],
    routines: ['Morning Routine', 'Night Routine', 'Weekend Routine']
  };

  const formatDate = (date) => {
    if (!date) return '';
    
    // Handle Firestore Timestamp or Date object
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  };

  // Move useAuth hook to component level
  const { isAuthenticated, isAdmin, isEditor } = useAuth();

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  const fetchBlogPosts = async () => {
    setLoading(true);
    try {
      if (!isAuthenticated) {
        throw new Error('You must be logged in to access blog posts');
      }
      
      if (!isAdmin && !isEditor) {
        throw new Error('You must be an admin or editor to manage blog posts');
      }
      
      const blogRef = collection(db, 'blogPosts');
      const snapshot = await getDocs(blogRef);
      const postsList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setBlogPosts(postsList);
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      setMessage({ text: `Error fetching blog posts: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleImageChange = (e) => {
    if (e.target.files[0]) {
      setFormData({
        ...formData,
        image: e.target.files[0]
      });
    }
  };

  const handleCategoryChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'category') {
      // Reset subcategory when main category changes
      setFormData({
        ...formData,
        category: value,
        subcategory: ''
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const getFullCategory = () => {
    if (!formData.category) return '';
    if (!formData.subcategory) return formData.category;
    return `${formData.category}/${formData.subcategory}`;
  };

  const resetForm = () => {
    setFormData({
      title: '',
      summary: '',
      content: '',
      author: '',
      image: null,
      featured: false,
      category: '',
      subcategory: ''
    });
    setEditingPost(null);
  };

  const handleAddPost = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ text: '', type: '' });

    try {
      // Validate form data
      if (!formData.title || !formData.content || !formData.author || !formData.category) {
        throw new Error('Please fill in all required fields');
      }

      let imageUrl = '';
      
      // Upload image if provided
      if (formData.image) {
        imageUrl = await uploadImage(formData.image, 'blog');
      }

      // Create the full category string
      const fullCategory = formData.subcategory 
        ? `${formData.category}/${formData.subcategory}`
        : formData.category;

      // Add new blog post to Firestore
      const postData = {
        title: formData.title,
        summary: formData.summary,
        content: formData.content,
        author: formData.author,
        imageUrl: imageUrl,
        featured: formData.featured,
        category: fullCategory,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await addDoc(collection(db, 'blogPosts'), postData);
      
      setMessage({ text: 'Blog post added successfully!', type: 'success' });
      resetForm();
      setShowForm(false);
      fetchBlogPosts();
    } catch (error) {
      console.error('Error adding blog post:', error);
      setMessage({ text: `Error adding blog post: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEditPost = (post) => {
    // Parse category and subcategory from the full category string
    let category = post.category || '';
    let subcategory = '';
    
    if (category.includes('/')) {
      const parts = category.split('/');
      category = parts[0];
      subcategory = parts[1];
    }
    
    setFormData({
      title: post.title,
      summary: post.summary || '',
      content: post.content,
      author: post.author,
      featured: post.featured || false,
      image: null,
      category: category,
      subcategory: subcategory
    });
    setEditingPost(post);
    setShowForm(true);
  };

  const handleUpdatePost = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ text: '', type: '' });

    try {
      if (!editingPost) return;

      // Create the full category string
      const fullCategory = formData.subcategory 
        ? `${formData.category}/${formData.subcategory}`
        : formData.category;

      let imageUrl = editingPost.imageUrl || '';
      
      if (formData.image) {
        // Delete old image if exists
        if (imageUrl) {
          try {
            const oldImageRef = ref(storage, imageUrl);
            await deleteObject(oldImageRef);
          } catch (error) {
            console.error('Error deleting old image:', error);
          }
        }
        
        // Upload new image
        imageUrl = await uploadImage(formData.image, 'blog');
      }

      const postRef = doc(db, 'blogPosts', editingPost.id);
      await updateDoc(postRef, {
        title: formData.title,
        summary: formData.summary,
        content: formData.content,
        author: formData.author,
        imageUrl: imageUrl,
        featured: formData.featured,
        category: fullCategory,
        updatedAt: new Date()
      });

      setMessage({ text: 'Blog post updated successfully!', type: 'success' });
      resetForm();
      setShowForm(false);
      fetchBlogPosts();
      setEditingPost(null);
    } catch (error) {
      console.error('Error updating blog post:', error);
      setMessage({ text: `Error updating blog post: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async (post) => {
    setDeleteModal({
      isOpen: true,
      post
    });
  };

  const confirmDeletePost = async () => {
    const post = deleteModal.post;
    setLoading(true);
    try {
      // Delete image from storage if exists
      if (post.imageUrl) {
        try {
          const imageRef = ref(storage, post.imageUrl);
          await deleteObject(imageRef);
        } catch (error) {
          console.error('Error deleting image:', error);
        }
      }
      
      // Delete blog post from Firestore
      await deleteDoc(doc(db, 'blogPosts', post.id));
      
      setMessage({ text: 'Blog post deleted successfully!', type: 'success' });
      fetchBlogPosts();
    } catch (error) {
      console.error('Error deleting blog post:', error);
      setMessage({ text: `Error deleting blog post: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
      setDeleteModal({ isOpen: false, post: null });
    }
  };

  return (
    <Container darkMode={darkMode}>
      <Title darkMode={darkMode}>Blog Management</Title>
      
      {message.text && (
        <Message type={message.type}>{message.text}</Message>
      )}
      
      <Button 
        primary 
        onClick={() => {
          resetForm();
          setShowForm(!showForm);
        }}
        style={{ maxWidth: '200px' }}
      >
        {showForm ? 'Cancel' : 'Add New Blog Post'}
      </Button>
      
      {showForm && (
        <Form onSubmit={editingPost ? handleUpdatePost : handleAddPost} darkMode={darkMode}>
          <FormGroup darkMode={darkMode}>
            <label>Title*</label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>
          
          <FormGroup darkMode={darkMode}>
            <label>Summary (HTML supported)</label>
            <TextArea
              name="summary"
              value={formData.summary}
              onChange={handleInputChange}
              rows="3"
              darkMode={darkMode}
              placeholder="<p>Enter summary with HTML tags if needed</p>"
            />
          </FormGroup>
          
          <FormGroup darkMode={darkMode}>
            <label>Content* (HTML supported)</label>
            <TextArea
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
              placeholder="<p>Enter content with HTML tags</p>"
            />
          </FormGroup>
          
          <FormGroup darkMode={darkMode}>
            <label>Author*</label>
            <Input
              type="text"
              name="author"
              value={formData.author}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>
          
          <FormGroup>
            <label>Featured Image</label>
            <Input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
            />
            {editingPost && editingPost.imageUrl && (
              <p>Current image: {editingPost.imageUrl.split('/').pop()}</p>
            )}
          </FormGroup>
          
          <FormGroup darkMode={darkMode}>
            <label style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                style={{ marginRight: spacing.xs, width: 'auto' }}
              />
              Featured Post
            </label>
          </FormGroup>
          
          <CategorySelect>
            <FormGroup darkMode={darkMode}>
              <label>Main Category*</label>
              <Select 
                name="category"
                value={formData.category || ''}
                onChange={handleCategoryChange}
                required
                darkMode={darkMode}
              >
                <option value="">Select a category</option>
                {Object.keys(categoryStructure).map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </Select>
            </FormGroup>
            
            {formData.category && (
              <FormGroup darkMode={darkMode}>
                <label>Subcategory</label>
                <Select 
                  name="subcategory"
                  value={formData.subcategory || ''}
                  onChange={handleInputChange}
                  darkMode={darkMode}
                >
                  <option value="">None</option>
                  {categoryStructure[formData.category]?.map(subcategory => (
                    <option key={subcategory} value={subcategory}>
                      {subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}
                    </option>
                  ))}
                </Select>
              </FormGroup>
            )}
          </CategorySelect>
          
          <FormGroup>
            <Button primary type="submit" disabled={loading}>
              {loading ? 'Processing...' : (editingPost ? 'Update Blog Post' : 'Add Blog Post')}
            </Button>
          </FormGroup>
        </Form>
      )}
      
      {loading && !showForm ? (
        <p>Loading blog posts...</p>
      ) : (
        <>
          <h3>Blog Posts ({blogPosts.length})</h3>
          <BlogGrid>
            {currentPosts.map(post => (
              <BlogCard key={post.id} darkMode={darkMode}>
                {post.imageUrl && (
                  <BlogImage src={post.imageUrl} alt={post.title} />
                )}
                <h4>{post.title}</h4>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  fontSize: '0.8rem',
                  marginBottom: '0.5rem'
                }}>
                  <span>By: {post.author}</span>
                  <span>{formatDate(post.createdAt || post.updatedAt)}</span>
                </div>
                {/* Replace the raw text with rendered HTML */}
                <div dangerouslySetInnerHTML={{ 
                  __html: post.summary || post.content.substring(0, 100) + '...' 
                }} />
                
                {/* Display category tags */}
                <div style={{ marginTop: '0.5rem' }}>
                  {post.category && (
                    <>
                      {post.category.split('/').map((cat, index) => (
                        <CategoryTag 
                          key={index} 
                          main={index === 0} 
                          darkMode={darkMode}
                        >
                          {cat}
                        </CategoryTag>
                      ))}
                    </>
                  )}
                  {post.featured && (
                    <span style={{ 
                      display: 'inline-block',
                      background: colors.primary.main,
                      color: 'white',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '0.7rem',
                      marginLeft: '5px'
                    }}>
                      Featured
                    </span>
                  )}
                </div>
                <ButtonGroup>
                  <Button onClick={() => handleEditPost(post)}>Edit</Button>
                  <DeleteButton onClick={() => handleDeletePost(post)}>Delete</DeleteButton>
                </ButtonGroup>
              </BlogCard>
            ))}
          </BlogGrid>
          
          {/* Add pagination */}
          {blogPosts.length > postsPerPage && (
            <PaginationContainer>
              <PageButton 
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                darkMode={darkMode}
              >
                &lt;
              </PageButton>
              
              {[...Array(totalPages)].map((_, index) => {
                const pageNumber = index + 1;
                
                // Show first, last, current and adjacent pages
                if (
                  pageNumber === 1 || 
                  pageNumber === totalPages || 
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                ) {
                  return (
                    <PageButton
                      key={pageNumber}
                      active={currentPage === pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      darkMode={darkMode}
                    >
                      {pageNumber}
                    </PageButton>
                  );
                }
                
                // Show ellipsis
                if (
                  (pageNumber === 2 && currentPage > 3) ||
                  (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                ) {
                  return <span key={pageNumber} style={{ alignSelf: 'center' }}>...</span>;
                }
                
                return null;
              })}
              
              <PageButton 
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                darkMode={darkMode}
              >
                &gt;
              </PageButton>
            </PaginationContainer>
          )}
        </>
      )}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, post: null })}
        onConfirm={confirmDeletePost}
        itemName={deleteModal.post?.title || ''}
        darkMode={darkMode}
      />
    </Container>
  );
};

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const PageButton = styled.button`
  background-color: ${props => 
    props.active 
      ? props.darkMode ? colors.primary.dark : colors.primary.main
      : props.darkMode ? colors.neutral.dark : colors.neutral.white
  };
  color: ${props => 
    props.active 
      ? 'white' 
      : props.darkMode ? colors.neutral.white : colors.neutral.dark
  };
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  padding: 5px 10px;
  min-width: 35px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.5 : 1};
  
  &:hover {
    background-color: ${props => 
      props.active 
        ? props.darkMode ? colors.primary.darker : colors.primary.dark
        : props.darkMode ? colors.neutral.darker : colors.neutral.light
    };
  }
`;

export default BlogManager;









































