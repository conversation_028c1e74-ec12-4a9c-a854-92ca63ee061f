import React, { useEffect, useState, useRef } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  FaSearch, 
  FaTimes, 
  FaSort, 
  FaFilter, 
  FaSortAmountDown, 
  FaSortAmountUp,
  FaInbox,
  FaEnvelopeOpen,
  FaEnvelope,
  FaStar,
  FaArchive,
  FaChevronLeft,
  FaChevronRight,
  FaCalendarAlt
} from 'react-icons/fa';
import { colors, spacing, breakpoints } from '../../../styles';
import { createGlobalStyle } from 'styled-components';
import { createPortal } from 'react-dom';

const FilterSidebar = styled.div`
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  border-radius: 8px;
  padding: ${props => props.sidebarOpen ? spacing.md : spacing.sm};
  height: fit-content;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: ${props => props.sidebarOpen ? 'visible' : 'hidden'};
  
  @media (max-width: ${breakpoints.md}) {
    padding: ${props => props.sidebarOpen ? spacing.sm : '8px'};
    width: ${props => props.sidebarOpen ? '100%' : '60px'};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.sm};
  margin-bottom: ${spacing.md};
`;

// Define consistent colors for tabs to match action buttons
const TAB_COLORS = {
  all: null,  // Set to null to use default styling instead of clear color
  unread: colors.info?.main || '#0088ff',     // Blue for unread
  read: colors.success?.main || '#4CAF50',    // Green for read
  starred: colors.warning?.main || '#ffbb00', // Yellow for starred
  archived: '#9c27b0'                         // Purple for archived
};

const Tab = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: ${props => props.sidebarOpen ? 'flex-start' : 'center'};
  padding: ${props => props.sidebarOpen ? `${spacing.sm} ${spacing.md}` : spacing.sm};
  background: ${props => 
    props.active 
      ? props.darkMode ? colors.primary.dark : colors.primary.light
      : 'transparent'
  };
  color: ${props => {
    // Always use tab color if available, regardless of active state
    if (props.tabColor) {
      return props.tabColor;
    }
    // Default colors for tabs without specific colors (like "all")
    return props.active
      ? props.darkMode ? colors.neutral.white : colors.primary.dark
      : props.darkMode ? colors.neutral.light : colors.neutral.dark;
  }};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  font-weight: ${props => props.active ? '600' : '400'};
  
  svg {
    color: ${props => props.tabColor || (props.active 
      ? props.darkMode ? colors.neutral.white : colors.primary.dark
      : props.darkMode ? colors.neutral.light : colors.neutral.dark)};
  }
  
  span {
    margin-left: ${spacing.sm};
    display: ${props => props.sidebarOpen ? 'inline' : 'none'};
  }
  
  .count {
    margin-left: auto;
    background: ${props => 
      props.active 
        ? props.darkMode ? colors.primary.light : colors.primary.dark
        : props.darkMode ? colors.neutral.darker : colors.neutral.light
    };
    color: ${props => 
      props.active 
        ? props.darkMode ? colors.neutral.white : colors.neutral.white
        : props.darkMode ? colors.neutral.light : colors.neutral.dark
    };
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    display: ${props => props.sidebarOpen ? 'inline' : 'none'};
  }
`;

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: ${spacing.md};
  display: ${props => props.sidebarOpen ? 'block' : 'none'};
`;

const SearchInputWrapper = styled.div`
  position: relative;
  width: 100%;
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 4px;
`;

const CustomSearchInput = styled.div`
  width: 100%;
  padding: ${spacing.sm} ${spacing.md};
  padding-left: 35px;
  padding-right: ${props => props.hasValue ? '40px' : spacing.md};
  border: 1px solid ${props => props.darkMode ? colors.neutral.medium : colors.neutral.light};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  min-height: 38px;
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
  
  &:empty:before {
    content: attr(data-placeholder);
    color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.medium};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.medium};
`;

const ClearButton = styled.button`
  position: absolute;
  right: ${spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.medium};
  cursor: pointer;
  display: ${props => props.visible ? 'block' : 'none'};
  
  &:hover {
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  }
`;

const FilterGroup = styled.div`
  margin-bottom: ${spacing.md};
  display: ${props => props.sidebarOpen ? 'block' : 'none'};
  position: relative; // Add position relative
  
  h3 {
    font-size: 14px;
    color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
    margin-bottom: ${spacing.sm};
  }
`;

const CustomDropdown = styled.div`
  position: relative;
  width: 100%;
  z-index: 10; // Add z-index to the container
`;

const DropdownHeader = styled.div`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.medium : colors.neutral.light};
  border-radius: 4px;
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`;

const DropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  border: 1px solid ${props => props.darkMode ? colors.neutral.medium : colors.neutral.light};
  border-top: none;
  border-radius: 0 0 4px 4px;
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  z-index: 9999; // Use a very high z-index
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`;

const DropdownItem = styled.div`
  padding: ${spacing.sm};
  cursor: pointer;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  
  &:hover {
    background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.light};
  }
`;

const SortButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.xs};
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.light};
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: auto;
  height: 28px;
  width: 28px;
`;

const FilterContainer = styled.div`
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  border-radius: 8px;
  padding: ${props => props.sidebarOpen ? spacing.md : spacing.sm};
  height: fit-content;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: ${props => props.sidebarOpen ? 'visible' : 'hidden'};
  
  @media (max-width: ${breakpoints.md}) {
    padding: ${props => props.sidebarOpen ? spacing.sm : '8px'};
  }
`;

const FilterLabel = styled.h3`
  font-size: 14px;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  margin-bottom: ${spacing.sm};
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
`;

const FilterHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.sm};
`;

const SearchInputOverride = createGlobalStyle`
  input[type="text"] {
    background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest} !important;
  }
  
  select {
    background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest} !important;
    opacity: ${props => props.darkMode ? 0.9 : 1} !important;
  }
  
  select option {
    background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest} !important;
    opacity: ${props => props.darkMode ? 0.9 : 1} !important;
  }
  
  /* Target webkit browsers specifically */
  @media screen and (-webkit-min-device-pixel-ratio:0) {
    select {
      background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    }
    
    select option {
      background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    }
  }
  
  /* Firefox specific styling */
  @-moz-document url-prefix() {
    select {
      background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    }
    
    select option {
      background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white} !important;
    }
  }
`;

// Add a toggle button to the sidebar
const SidebarHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};
`;

const SidebarTitle = styled.h3`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin: 0;
  font-size: 1rem;
`;

const ToggleButton = styled(motion.button)`
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  cursor: pointer;
  padding: ${spacing.xs};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MessageFilters = ({ 
  darkMode, 
  activeTab, 
  setActiveTab, 
  searchTerm, 
  setSearchTerm, 
  dateFilter, 
  setDateFilter, 
  sortOrder, 
  setSortOrder,
  sidebarOpen,
  setSidebarOpen, // Make sure this prop is passed from the parent
  searchQuery,
  handleSearchChange,
  clearSearch,
  unreadCount,
  readCount,
  starredCount,
  archivedCount,
  allMessagesCount,
  showFilters = true, // New prop with default value
  isDropdown = false // New prop with default value
}) => {
  const tabs = [
    { id: 'all', label: 'All Messages', icon: <FaInbox /> },
    { id: 'unread', label: 'Unread', icon: <FaEnvelope /> },
    { id: 'read', label: 'Read', icon: <FaEnvelopeOpen /> },
    { id: 'starred', label: 'Starred', icon: <FaStar /> },
    { id: 'archived', label: 'Archived', icon: <FaArchive /> }
  ];
  
  const dateOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' }
  ];
  
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest');
  };
  
  const handleCustomInputChange = (e) => {
    const text = e.target.textContent;
    handleSearchChange({ target: { value: text } });
  };

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownHeaderRef = useRef(null);

  useEffect(() => {
    if (isDropdownOpen && dropdownHeaderRef.current) {
      const rect = dropdownHeaderRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [isDropdownOpen]);

  useEffect(() => {
    // This is a hack to force the select dropdown to use dark mode colors
    if (darkMode) {
      // Create a style element
      const style = document.createElement('style');
      // Add CSS to override the dropdown background
      style.textContent = `
        select option {
          background-color: ${colors.neutral.darker} !important;
          color: ${colors.neutral.white} !important;
        }
        
        select {
          background-color: ${colors.neutral.darker} !important;
          color: ${colors.neutral.white} !important;
        }
      `;
      // Add the style to the document head
      document.head.appendChild(style);
      
      // Clean up function
      return () => {
        document.head.removeChild(style);
      };
    }
  }, [darkMode]);

  // Add a click outside handler to close the dropdown when clicking elsewhere
  useEffect(() => {
    if (isDropdownOpen) {
      const handleClickOutside = (event) => {
        // Check if the click was outside the dropdown and not in the portal
        const dropdowns = document.querySelectorAll('.custom-dropdown');
        const portals = document.querySelectorAll('.dropdown-portal');
        let clickedOutside = true;
        
        dropdowns.forEach(dropdown => {
          if (dropdown.contains(event.target)) {
            clickedOutside = false;
          }
        });
        
        portals.forEach(portal => {
          if (portal.contains(event.target)) {
            clickedOutside = false;
          }
        });
        
        if (clickedOutside) {
          setIsDropdownOpen(false);
        }
      };
      
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isDropdownOpen]);

  return (
    <>
      <SearchInputOverride darkMode={darkMode} />
      <FilterSidebar darkMode={darkMode} sidebarOpen={sidebarOpen}>
        {sidebarOpen && (
          <SidebarHeader>
            <SidebarTitle darkMode={darkMode}>Messages</SidebarTitle>
            <ToggleButton 
              onClick={() => setSidebarOpen(false)}
              darkMode={darkMode}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaChevronLeft />
            </ToggleButton>
          </SidebarHeader>
        )}
        
        {!sidebarOpen && (
          <ToggleButton 
            onClick={() => setSidebarOpen(true)}
            darkMode={darkMode}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            style={{ margin: '0 auto', display: 'block' }}
          >
            <FaChevronRight />
          </ToggleButton>
        )}
        
        <TabsContainer>
          {tabs.map(tab => (
            <Tab
              key={tab.id}
              active={activeTab === tab.id}
              darkMode={darkMode}
              onClick={() => setActiveTab(tab.id)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              sidebarOpen={sidebarOpen}
              tabColor={TAB_COLORS[tab.id]}
            >
              {tab.icon}
              <span>{tab.label}</span>
              {tab.id === 'all' && allMessagesCount > 0 && (
                <span className="count">{allMessagesCount}</span>
              )}
              {tab.id === 'unread' && unreadCount > 0 && (
                <span className="count">{unreadCount}</span>
              )}
              {tab.id === 'read' && readCount > 0 && (
                <span className="count">{readCount}</span>
              )}
              {tab.id === 'starred' && starredCount > 0 && (
                <span className="count">{starredCount}</span>
              )}
              {tab.id === 'archived' && archivedCount > 0 && (
                <span className="count">{archivedCount}</span>
              )}
            </Tab>
          ))}
        </TabsContainer>
        
        {/* Only show these filters if showFilters is true */}
        {showFilters && (
          <>
            <SearchContainer sidebarOpen={sidebarOpen}>
              <SearchInputWrapper darkMode={darkMode}>
                <SearchIcon darkMode={darkMode}>
                  <FaSearch />
                </SearchIcon>
                <CustomSearchInput
                  contentEditable
                  suppressContentEditableWarning
                  data-placeholder="Search messages..."
                  onInput={handleCustomInputChange}
                  darkMode={darkMode}
                  hasValue={!!searchQuery}
                  ref={(el) => {
                    if (el) {
                      // Only set content if there's a query, otherwise clear it
                      if (searchQuery) {
                        el.textContent = searchQuery;
                      } else {
                        el.textContent = '';
                      }
                    }
                  }}
                />
                {searchQuery && (
                  <ClearButton 
                    onClick={(e) => {
                      e.preventDefault();
                      clearSearch();
                      // Focus back on the input after clearing
                      const searchInputElement = document.querySelector('[data-placeholder="Search messages..."]');
                      if (searchInputElement) {
                        searchInputElement.focus();
                      }
                    }}
                    visible={!!searchQuery}
                    darkMode={darkMode}
                  >
                    <FaTimes />
                  </ClearButton>
                )}
              </SearchInputWrapper>
            </SearchContainer>
            
            <FilterGroup sidebarOpen={sidebarOpen} darkMode={darkMode}>
              <FilterHeader>
                <FilterLabel darkMode={darkMode}>
                  <FaCalendarAlt /> Date Range
                </FilterLabel>
                <SortButton
                  darkMode={darkMode}
                  onClick={toggleSortOrder}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  title={sortOrder === 'newest' ? "Newest First" : "Oldest First"}
                >
                  {sortOrder === 'newest' ? <FaSortAmountDown /> : <FaSortAmountUp />}
                </SortButton>
              </FilterHeader>
              <CustomDropdown className="custom-dropdown">
                <DropdownHeader 
                  ref={dropdownHeaderRef}
                  darkMode={darkMode} 
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <FaCalendarAlt />
                    {dateOptions.find(option => option.value === dateFilter)?.label || 'Date Range'}
                  </span>
                  <FaSort />
                </DropdownHeader>
                
                {isDropdownOpen && createPortal(
                  <div 
                    style={{
                      position: 'absolute',
                      top: `${dropdownPosition.top}px`,
                      left: `${dropdownPosition.left}px`,
                      width: `${dropdownPosition.width}px`,
                      zIndex: 9999,
                      borderRadius: '0 0 4px 4px',
                      border: `1px solid ${darkMode ? colors.neutral.medium : colors.neutral.light}`,
                      borderTop: 'none',
                      background: darkMode ? colors.neutral.darker : colors.neutral.white,
                      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                      maxHeight: '200px',
                      overflowY: 'auto'
                    }}
                    className="dropdown-portal"
                  >
                    {dateOptions.map(option => (
                      <div
                        key={option.value}
                        style={{
                          padding: spacing.sm,
                          cursor: 'pointer',
                          color: darkMode ? colors.neutral.white : colors.neutral.darkest,
                          backgroundColor: darkMode ? colors.neutral.darker : colors.neutral.white
                        }}
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent event bubbling
                          setDateFilter(option.value);
                          setIsDropdownOpen(false);
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.background = darkMode ? colors.neutral.dark : colors.neutral.light;
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.background = darkMode ? colors.neutral.darker : colors.neutral.white;
                        }}
                      >
                        {option.label}
                      </div>
                    ))}
                  </div>,
                  document.body
                )}
              </CustomDropdown>
            </FilterGroup>
          </>
        )}
      </FilterSidebar>
    </>
  );
};

export default MessageFilters;










































































