export const colors = {
  // Primary colors
  primary: {
    main: '#F8B3C5', // Soft pink
    light: '#FDD3DE',
    dark: '#000000',
  },
  // Secondary colors
  secondary: {
    main: '#A0D9D9', // Mint/teal
    light: '#C5E8E8',
    dark: '#000000',
  },
  // Neutrals
  neutral: {
    white: '#FFFFFF',
    lightest: '#F9F9F9',
    light: '#F0F0F0',
    medium: '#DDDDDD',
    gray: '#999999',
    darkGray: '#666666',
    dark: '#333333',
    black: '#000000',
  },
  // Feedback colors
  feedback: {
    success: '#4CAF50',
    warning: '#FFC107',
    error: '#F44336',
    info: '#2196F3',
  },
  // Add these properties to fix the error
  success: {
    main: '#4CAF50',
    light: '#E8F5E9',
    dark: '#2E7D32',
  },
  error: {
    main: '#FF6B6B', // Lighter red for default state
    light: '#FFEBEE',
    dark: '#C62828', // Deeper red for hover state
  },
  info: {
    main: '#2196F3',
    light: '#E3F2FD',
    dark: '#1565C0',
  }
};


export const spacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '3rem',
  '3xl': '4rem'
};

export const typography = {
  fontFamily: {
    primary: "'Montserrat', sans-serif",
    secondary: "'Playfair Display', serif"
  },
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    md: '1.125rem',
    lg: '1.25rem',
    xl: '1.5rem',
    '2xl': '1.875rem',
    '3xl': '2.25rem',
    '4xl': '3rem'
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
};

export const breakpoints = {
  xs: '320px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px'
};

export const shadows = {
  sm: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  md: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
  lg: '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
  xl: '0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)'
};







