import styled, { css } from 'styled-components';
import { colors, spacing, typography } from '../../styles';

// Base button component
const ButtonBase = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: ${typography.fontWeight.medium};
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Button variants
const variants = {
  primary: css`
    background-color: ${colors.primary.main};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: ${colors.primary.dark};
    }
  `,
  secondary: css`
    background-color: ${colors.secondary.main};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: ${colors.secondary.dark};
    }
  `,
  outline: css`
    background-color: transparent;
    color: ${colors.primary.main};
    border: 1px solid ${colors.primary.main};
    
    &:hover:not(:disabled) {
      background-color: ${props => props.darkMode ? 'rgba(248, 179, 197, 0.2)' : colors.primary.light};
    }
  `,
  text: css`
    background-color: transparent;
    color: ${colors.primary.main};
    
    &:hover:not(:disabled) {
      background-color: ${props => props.darkMode ? 'rgba(248, 179, 197, 0.2)' : colors.primary.light};
    }
  `
};

// Button sizes
const sizes = {
  small: css`
    padding: ${spacing.xs} ${spacing.md};
    font-size: ${typography.fontSize.sm};
  `,
  medium: css`
    padding: ${spacing.sm} ${spacing.lg};
    font-size: ${typography.fontSize.base};
  `,
  large: css`
    padding: ${spacing.md} ${spacing.xl};
    font-size: ${typography.fontSize.md};
  `
};

export const Button = styled(ButtonBase)`
  ${props => variants[props.variant || 'primary']}
  ${props => sizes[props.size || 'medium']}
  width: ${props => props.fullWidth ? '100%' : 'auto'};
`;

export default Button;

