/**
 * Helper functions for managing product variants
 */

/**
 * Generate a unique variant ID
 * @returns {string} A unique ID for a variant
 */
export const generateVariantId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

/**
 * Create a new variant option
 * @param {string} name - The name of the option (e.g., "<PERSON>ze", "Color", "Flavor")
 * @param {Array} values - Array of possible values for this option
 * @returns {Object} A new variant option object
 */
export const createVariantOption = (name, values = []) => {
  return {
    name,
    values,
  };
};

/**
 * Create a new variant combination
 * @param {Object} options - Object with option values (e.g., { size: "Small", color: "Red" })
 * @param {number} price - Price adjustment for this variant (can be positive or negative)
 * @param {number} stock - Stock quantity for this variant
 * @param {string} sku - SKU for this variant
 * @returns {Object} A new variant combination object
 */
export const createVariantCombination = (
  options,
  price = 0,
  stock = 0,
  sku = ""
) => {
  return {
    id: generateVariantId(),
    options,
    price,
    stock,
    sku,
  };
};

/**
 * Generate all possible combinations of variant options
 * @param {Array} options - Array of variant options
 * @returns {Array} Array of all possible combinations
 */
export const generateAllCombinations = (options) => {
  if (!options || options.length === 0) return [];

  // Start with an array containing an empty object
  let combinations = [{}];

  // For each option
  options.forEach((option) => {
    const { name, values } = option;

    // Create a new array to store the new combinations
    const newCombinations = [];

    // For each existing combination
    combinations.forEach((combination) => {
      // For each value of the current option
      values.forEach((value) => {
        // Create a new combination by adding the current value to the existing combination
        newCombinations.push({
          ...combination,
          [name.toLowerCase()]: value,
        });
      });
    });

    // Replace the old combinations with the new ones
    combinations = newCombinations;
  });

  // Convert combinations to the format expected by createVariantCombination
  return combinations.map((options) => createVariantCombination(options));
};

/**
 * Calculate the final price for a product variant
 * @param {number} basePrice - The base price of the product
 * @param {number} variantPriceAdjustment - The price adjustment for the variant
 * @returns {number} The final price
 */
export const calculateVariantPrice = (
  basePrice,
  variantPriceAdjustment = 0
) => {
  return basePrice + variantPriceAdjustment;
};

/**
 * Find a variant by its options
 * @param {Array} variants - Array of variant combinations
 * @param {Object} selectedOptions - The selected options (e.g., { size: "Small", color: "Red" })
 * @returns {Object|null} The matching variant or null if not found
 */
export const findVariantByOptions = (variants, selectedOptions) => {
  if (!variants || variants.length === 0 || !selectedOptions) return null;

  return variants.find((variant) => {
    // Check if all selected options match this variant
    return Object.keys(selectedOptions).every((optionName) => {
      return variant.options[optionName] === selectedOptions[optionName];
    });
  });
};

/**
 * Check if a variant is available (in stock)
 * @param {Object} variant - The variant to check
 * @returns {boolean} True if the variant is available
 */
export const isVariantAvailable = (variant) => {
  if (!variant) return false;
  return variant.stock > 0;
};

/**
 * Format variant options for display
 * @param {Object} options - The variant options
 * @returns {string} Formatted string of options
 */
export const formatVariantOptions = (options) => {
  if (!options) return "";

  return Object.entries(options)
    .map(
      ([name, value]) =>
        `${name.charAt(0).toUpperCase() + name.slice(1)}: ${value}`
    )
    .join(", ");
};

/**
 * Organize variant media with primary image first
 * @param {Object} variant - The variant object
 * @returns {Array} Array of media items with primary image first
 */
export const organizeVariantMedia = (variant) => {
  if (!variant) return [];

  const mediaItems = [];

  // Add primary image first if it exists
  if (variant.imageUrl) {
    mediaItems.push({
      url: variant.imageUrl,
      type: "image",
      alt: "Primary Variant Image",
      isPrimary: true,
    });
  }

  // Add the rest of the media
  if (variant.media && variant.media.length > 0) {
    // Filter out any duplicates of the primary image
    const additionalMedia = variant.media.filter(
      (item) => item.url !== variant.imageUrl
    );

    mediaItems.push(...additionalMedia);
  }

  return mediaItems;
};

/**
 * Get all media items for a product or variant
 * @param {Object} product - The product object
 * @param {Object} selectedVariant - The selected variant (optional)
 * @returns {Array} Array of media items
 */
export const getProductMediaItems = (product, selectedVariant) => {
  if (!product) return [];

  // If a variant is selected, prioritize its media
  if (selectedVariant) {
    // Get organized variant media (primary image first, then additional media)
    const variantMedia = organizeVariantMedia(selectedVariant);

    if (variantMedia.length > 0) {
      return variantMedia;
    }
  }

  // If product has media, use that
  if (product.media && product.media.length > 0) {
    // If product has a primary image, ensure it's first
    if (product.imageUrl) {
      const primaryImageExists = product.media.some(
        (item) => item.url === product.imageUrl
      );

      if (!primaryImageExists) {
        return [
          {
            url: product.imageUrl,
            type: "image",
            alt: product.name,
            isPrimary: true,
          },
          ...product.media,
        ];
      }

      // Reorder to ensure primary image is first
      return product.media.sort((a, b) => {
        if (a.url === product.imageUrl) return -1;
        if (b.url === product.imageUrl) return 1;
        return 0;
      });
    }

    return product.media;
  }

  // Fallback to the main product image
  if (product.imageUrl) {
    return [
      {
        url: product.imageUrl,
        type: "image",
        alt: product.name,
        isPrimary: true,
      },
    ];
  }

  // No media available
  return [];
};
