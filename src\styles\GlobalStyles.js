import { createGlobalStyle } from 'styled-components';
import { typography } from './index';

const GlobalStyles = createGlobalStyle`
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
  
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  html, body {
    font-family: ${typography.fontFamily.primary};
    font-size: 16px;
    line-height: 1.5;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body {
    color: ${props => props.darkMode ? '#FFFFFF' : '#000000'};
    background-color: ${props => props.darkMode ? '#000000' : '#FFFFFF'};
  }
  
  
  h1, h2, h3, h4, h5, h6 {
    font-family: ${typography.fontFamily.secondary};
    font-weight: ${typography.fontWeight.bold};
    margin-bottom: 0.5em;
    color: ${props => props.darkMode ? '#FFFFFF' : '#000000'};
  }
  
  h1 {
    font-size: ${typography.fontSize['3xl']};
  }
  
  h2 {
    font-size: ${typography.fontSize['2xl']};
  }
  
  h3 {
    font-size: ${typography.fontSize.xl};
  }
  
  h4 {
    font-size: ${typography.fontSize.lg};
  }
  
  p {
    margin-bottom: 1em;
    color: ${props => props.darkMode ? '#FFFFFF' : '#000000'};
  }
  
  a {
    color: ${props => props.darkMode ? '#FFFFFF' : '#000000'};
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }

  .dark-mode {
    background-color: #000000 !important;
    color: #FFFFFF !important;
  }
`;

export default GlobalStyles;







