import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import Button from "../components/ui/Button";
import { FaGoogle, FaEye, FaEyeSlash } from "react-icons/fa";
import { colors } from "../styles";

const LoginContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const LoginTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const LoginForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  padding-right: 2.5rem; /* Make room for the eye icon */
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
`;

const ErrorMessage = styled.p`
  color: red;
  font-size: 0.875rem;
`;

const RegisterLink = styled.p`
  text-align: center;
  margin-top: 1.5rem;
`;

const GoogleButton = styled(Button)`
  background-color: ${(props) =>
    props.$darkMode ? colors.primary.dark : colors.primary.main};
  color: ${colors.neutral.white};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 1rem;

  &:hover {
    background-color: ${(props) =>
      props.$darkMode ? colors.primary.main : colors.primary.light};
  }
`;

const PasswordInputWrapper = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
`;

const PasswordToggleButton = styled.button`
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: ${(props) =>
    props.$darkMode ? colors.neutral.gray400 : colors.neutral.gray600};
  z-index: 1; /* Ensure button is clickable */
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${colors.primary.main};
  }
`;

const OrDivider = styled.div`
  display: flex;
  align-items: center;
  margin: 1rem 0;

  &:before,
  &:after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #ddd;
  }

  span {
    padding: 0 10px;
    color: #777;
    font-size: 0.9rem;
  }
`;

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [localError, setLocalError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const {
    login,
    signInWithGoogle,
    error: authError,
    getSavedLocation,
    clearSavedLocation,
  } = useAuth();
  const { darkMode } = useTheme();

  // Check for redirect parameter in URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const redirectPath = searchParams.get("redirect");

    if (redirectPath) {
      // Convert the redirect parameter to a full path
      const fullRedirectPath = `/${redirectPath}`;
      localStorage.setItem("authRedirectPath", fullRedirectPath);
    }
  }, [location]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLocalError("");
    setLoading(true);

    try {
      const success = await login(email, password);
      if (success) {
        // Get the saved redirect path or default to account page
        const redirectPath = getSavedLocation();
        // Clear the saved path to avoid unexpected redirects in the future
        clearSavedLocation();
        // Navigate to the redirect path
        navigate(redirectPath);
      } else {
        setLocalError("Login failed. Please check your credentials.");
      }
    } catch (error) {
      setLocalError("An unexpected error occurred.");
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const success = await signInWithGoogle();
      if (success) {
        // Get the saved redirect path or default to account page
        const redirectPath = getSavedLocation();
        // Clear the saved path to avoid unexpected redirects in the future
        clearSavedLocation();
        // Navigate to the redirect path
        navigate(redirectPath);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Display either local error or auth context error
  const displayError = localError || authError;

  return (
    <LoginContainer>
      <LoginHeader>
        <LoginTitle>Sign In</LoginTitle>
        <p>Welcome back! Please sign in to your account.</p>
      </LoginHeader>

      {displayError && <ErrorMessage>{displayError}</ErrorMessage>}

      <LoginForm onSubmit={handleSubmit}>
        <FormGroup>
          <label htmlFor="email">Email</label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </FormGroup>

        <FormGroup>
          <label htmlFor="password">Password</label>
          <PasswordInputWrapper>
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              $darkMode={darkMode}
            />
            <PasswordToggleButton
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              $darkMode={darkMode}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggleButton>
          </PasswordInputWrapper>
          <div style={{ textAlign: "left", marginTop: "0.25rem" }}>
            <Link
              to="/forgot-password"
              style={{ color: colors.primary.main, fontSize: "0.9rem" }}
            >
              Forgot Password?
            </Link>
          </div>
        </FormGroup>

        <Button type="submit" disabled={loading}>
          {loading ? "Signing in..." : "Sign In"}
        </Button>
      </LoginForm>

      <OrDivider>
        <span>OR</span>
      </OrDivider>

      <GoogleButton
        type="button"
        onClick={handleGoogleSignIn}
        disabled={loading}
        $darkMode={darkMode}
      >
        <FaGoogle style={{ color: "white" }} /> Sign in with Google
      </GoogleButton>

      <RegisterLink>
        Don't have an account? <Link to="/register">Register</Link>
      </RegisterLink>
    </LoginContainer>
  );
};

export default Login;
