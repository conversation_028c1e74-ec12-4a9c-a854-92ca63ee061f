import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../styles';
import { useTheme } from '../context/ThemeContext';
import { collection, getDocs, orderBy, query, where, limit } from 'firebase/firestore';
import { db } from '../firebase/config';
import { motion, AnimatePresence, useCycle } from 'framer-motion';
import { FaFilter, FaTimes, FaChevronLeft, FaChevronRight, FaAngleDown, FaAngleUp } from 'react-icons/fa';

// Main container with sidebar layout
const BlogLayout = styled.div`
  display: grid;
  grid-template-columns: ${props => props.sidebarVisible ? 'minmax(250px, 1fr) 3fr' : '1fr'};
  gap: ${spacing.lg};
  max-width: 1400px;
  margin: 0 auto;
  
  @media (max-width: ${breakpoints.lg}) {
    grid-template-columns: 1fr;
  }
`;

// Sidebar for filters
const Sidebar = styled(motion.div)`
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
  border-radius: 8px;
  padding: ${spacing.md};
  height: fit-content;
  position: sticky;
  top: 20px;
  
  @media (max-width: ${breakpoints.lg}) {
    position: relative;
    top: 0;
  }
`;

const SidebarTitle = styled.h3`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const CategorySection = styled(motion.div)`
  margin-bottom: ${spacing.md};
`;

const CategoryTitle = styled.div`
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: ${spacing.xs} 0;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const SubcategoryList = styled(motion.div)`
  margin-left: ${spacing.md};
  margin-top: ${spacing.xs};
`;

const FilterItem = styled(motion.div)`
  padding: ${spacing.xs} ${spacing.sm};
  margin-bottom: ${spacing.xs};
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  background: ${props => props.active 
    ? (props.darkMode ? colors.primary.dark : colors.primary.light) 
    : 'transparent'};
  
  color: ${props => props.active 
    ? (props.darkMode ? colors.neutral.white : colors.primary.dark)
    : (props.darkMode ? colors.neutral.white : colors.neutral.dark)};
  
  &:hover {
    background: ${props => props.darkMode ? colors.primary.darker : colors.primary.lighter};
  }
`;

const FilterIndicator = styled.span`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.darkMode ? colors.primary.main : colors.primary.dark};
  margin-right: ${spacing.xs};
  opacity: ${props => props.active ? 1 : 0};
`;

const MobileSidebarToggle = styled(motion.button)`
  display: flex; // Changed from 'none' to always show
  align-items: center;
  justify-content: center;
  background: ${props => props.darkMode ? colors.primary.dark : colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.sm} ${spacing.md};
  margin-bottom: ${spacing.md};
  cursor: pointer;
`;

const ContentArea = styled.div`
  width: 100%;
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const PageButton = styled(motion.button)`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: ${props => props.active 
    ? (props.darkMode ? colors.primary.dark : colors.primary.main)
    : 'transparent'};
  color: ${props => props.active 
    ? 'white' 
    : (props.darkMode ? colors.neutral.white : colors.neutral.dark)};
  border: 1px solid ${props => props.darkMode ? colors.primary.dark : colors.primary.main};
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background: ${props => props.darkMode ? colors.primary.dark : colors.primary.main};
    color: white;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Animation variants
const sidebarVariants = {
  open: {
    x: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  },
  closed: {
    x: "-100%",
    opacity: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
};

const categoryVariants = {
  open: {
    height: "auto",
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  },
  closed: {
    height: 0,
    opacity: 0,
    transition: {
      duration: 0.3
    }
  }
};

const itemVariants = {
  open: {
    y: 0,
    opacity: 1,
    transition: { type: "spring", stiffness: 300, damping: 24 }
  },
  closed: {
    y: 20,
    opacity: 0,
    transition: { duration: 0.2 }
  }
};

const BlogContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: ${spacing.lg};
`;

const BlogHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const BlogTitle = styled.h1`
  font-family: ${typography.fontFamily.secondary};
  font-size: 2.5rem;
  margin-bottom: ${spacing.sm};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const BlogGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${spacing.lg};
`;

const BlogCard = styled.div`
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const BlogImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
`;

const BlogContent = styled.div`
  padding: ${spacing.md};
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  
  h2 {
    font-size: 1.25rem;
    margin-bottom: ${spacing.sm};
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  }
`;

const ReadMoreButton = styled(Link)`
  margin-top: auto;
  background: ${props => props.darkMode ? colors.primary.dark : colors.primary.main};
  color: white;
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 4px;
  text-decoration: none;
  display: inline-block;
  margin-top: ${spacing.md};
  text-align: center;
  
  &:hover {
    background: ${props => props.darkMode ? colors.primary.darker : colors.primary.dark};
  }
`;

const CategoryTag = styled.span`
  display: inline-block;
  background: ${props => props.main 
    ? (props.darkMode ? colors.primary.dark : colors.primary.main)
    : (props.darkMode ? colors.primary.darker : colors.primary.light)};
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 5px;
  margin-bottom: 5px;
  width: fit-content;
`;

const Blog = () => {
  const { darkMode } = useTheme();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [sidebarVisible, setSidebarVisible] = useState(true);
  
  // Add a resize listener to track window width
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Determine posts per page based on screen size
  const getPostsPerPage = () => {
    if (windowWidth < parseInt(breakpoints.md)) {
      return 3; // 3 posts per page on smaller screens
    } else {
      return 8; // 6 posts per page on larger screens
    }
  };
  
  const postsPerPage = getPostsPerPage();
  
  // When screen size changes enough to affect postsPerPage, reset to page 1
  useEffect(() => {
    setCurrentPage(1);
  }, [postsPerPage]);
  
  // Filter state
  const [activeCategory, setActiveCategory] = useState('all');
  const [activeSubcategory, setActiveSubcategory] = useState('all');
  const [isOpen, toggleOpen] = useCycle(false, true);
  
  // Category expansion state
  const [expandedCategories, setExpandedCategories] = useState({
    skincare: true,
    makeup: false,
    wellness: false,
    haircare: false
  });
  
  // Category structure
  const categoryStructure = {
    skincare: ['all', 'cleansers', 'moisturizers', 'serums', 'masks', 'sunscreen', 'treatments'],
    makeup: ['all', 'foundation', 'concealer', 'eyeshadow', 'lipstick', 'eyes', 'lips', 'cheeks'],
    wellness: ['all', 'meditation', 'yoga', 'nutrition', 'fitness', 'mental-health', 'sleep'],
    haircare: ['all', 'shampoo', 'conditioner', 'treatments', 'styling', 'styling products'],
    bodycare: ['all', 'bath bombs', 'body wash', 'exfoliants'],
    tips: ['all', 'diy projects', 'product reviews', 'beauty hacks'],
    ingredients: ['all', 'active ingredients', 'natural ingredients', 'chemical ingredients'],
    routines: ['all', 'morning routine', 'night routine', 'weekend routine']
  };
  
  const formatDate = (date) => {
    if (!date) return '';
    
    // Handle Firestore Timestamp or Date object
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  };

  useEffect(() => {
    const fetchBlogPosts = async () => {
      setLoading(true);
      try {
        const blogRef = collection(db, 'blogPosts');
        const q = query(blogRef, orderBy('createdAt', 'desc'));
        const snapshot = await getDocs(q);
        const postsList = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        setPosts(postsList);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBlogPosts();
  }, []);
  
  const toggleCategory = (category) => {
    setExpandedCategories({
      ...expandedCategories,
      [category]: !expandedCategories[category]
    });
  };
  
  const handleCategoryClick = (category, subcategory = 'all') => {
    setActiveCategory(category);
    setActiveSubcategory(subcategory);
    setCurrentPage(1); // Reset to first page
    
    // Close mobile sidebar if open
    if (isOpen && window.innerWidth <= 992) {
      toggleOpen();
    }
  };
  
  // Filter posts by category and subcategory
  const filteredPosts = posts.filter(post => {
    if (activeCategory === 'all') return true;
    
    // Parse the category string
    const postCategoryParts = post.category ? post.category.split('/') : [];
    const mainCategory = postCategoryParts[0] || '';
    const subCategory = postCategoryParts[1] || '';
    
    // Check if main category matches
    if (activeCategory !== mainCategory) return false;
    
    // If we're not filtering by subcategory, show all posts in this main category
    if (activeSubcategory === 'all') return true;
    
    // Check if subcategory matches
    return subCategory.toLowerCase() === activeSubcategory.toLowerCase();
  });
  
  // Pagination logic
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top when changing page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  // Determine how many page buttons to show based on screen size
  const getVisiblePageRange = () => {
    // For mobile screens
    if (windowWidth < parseInt(breakpoints.sm)) {
      // Show only current page, prev and next buttons
      return 0;
    }
    // For tablet screens
    else if (windowWidth < parseInt(breakpoints.md)) {
      // Show current page and one on each side
      return 1;
    }
    // For desktop screens
    else {
      // Show current page and two on each side
      return 2;
    }
  };
  
  return (
    <BlogContainer>
      <BlogHeader>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <BlogTitle darkMode={darkMode}>Skincare Blog</BlogTitle>
          <p style={{ color: darkMode ? colors.neutral.lighter : colors.neutral.dark }}>
            Tips, tricks, and insights for your skincare journey
          </p>
        </motion.div>
      </BlogHeader>
      
      <MobileSidebarToggle 
        onClick={() => setSidebarVisible(!sidebarVisible)}
        darkMode={darkMode}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <FaFilter style={{ marginRight: spacing.xs }} /> 
        {sidebarVisible ? 'Hide Filters' : 'Show Filters'}
      </MobileSidebarToggle>
      
      <BlogLayout sidebarVisible={sidebarVisible}>
        <AnimatePresence>
          {sidebarVisible && (
            <Sidebar 
              darkMode={darkMode}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SidebarTitle darkMode={darkMode}>
                Categories
                <motion.button
                  onClick={() => setSidebarVisible(false)}
                  style={{ background: 'none', border: 'none', cursor: 'pointer' }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FaTimes color={darkMode ? colors.neutral.white : colors.neutral.dark} />
                </motion.button>
              </SidebarTitle>
              
              <FilterItem 
                active={activeCategory === 'all' && activeSubcategory === 'all'}
                darkMode={darkMode}
                onClick={() => handleCategoryClick('all')}
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.95 }}
              >
                <FilterIndicator active={activeCategory === 'all'} darkMode={darkMode} />
                All Posts
              </FilterItem>
              
              {Object.keys(categoryStructure).map(category => (
                <CategorySection key={category}>
                  <CategoryTitle 
                    darkMode={darkMode}
                    onClick={() => toggleCategory(category)}
                  >
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                    {expandedCategories[category] ? <FaAngleUp /> : <FaAngleDown />}
                  </CategoryTitle>
                  
                  <AnimatePresence>
                    {expandedCategories[category] && (
                      <SubcategoryList
                        initial="closed"
                        animate="open"
                        exit="closed"
                        variants={categoryVariants}
                      >
                        {categoryStructure[category].map(subcategory => (
                          <FilterItem
                            key={`${category}-${subcategory}`}
                            active={activeCategory === category && (activeSubcategory === subcategory || (subcategory === 'all' && activeSubcategory === 'all'))}
                            darkMode={darkMode}
                            onClick={() => handleCategoryClick(category, subcategory)}
                            whileHover={{ x: 5 }}
                            whileTap={{ scale: 0.95 }}
                            variants={itemVariants}
                          >
                            <FilterIndicator 
                              active={activeCategory === category && (activeSubcategory === subcategory || (subcategory === 'all' && activeSubcategory === 'all'))} 
                              darkMode={darkMode} 
                            />
                            {subcategory === 'all' ? 'All ' + category.charAt(0).toUpperCase() + category.slice(1) : subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}
                          </FilterItem>
                        ))}
                      </SubcategoryList>
                    )}
                  </AnimatePresence>
                </CategorySection>
              ))}
            </Sidebar>
          )}
        </AnimatePresence>
        
        <ContentArea>
          {loading ? (
            <p>Loading blog posts...</p>
          ) : (
            <>
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeCategory + activeSubcategory + currentPage}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <BlogGrid>
                    {currentPosts.length > 0 ? currentPosts.map((post, index) => (
                      <motion.div
                        key={post.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        whileHover={{ y: -5, transition: { duration: 0.2 } }}
                      >
                        <BlogCard darkMode={darkMode}>
                          {post.imageUrl && <BlogImage src={post.imageUrl} alt={post.title} />}
                          <BlogContent darkMode={darkMode}>
                            <h2>{post.title}</h2>
                            {post.summary ? (
                              <div dangerouslySetInnerHTML={{ __html: post.summary }} />
                            ) : (
                              <div dangerouslySetInnerHTML={{ __html: post.content?.substring(0, 150) + '...' }} />
                            )}
                            <div style={{ 
                              display: 'flex', 
                              justifyContent: 'space-between', 
                              fontSize: '0.9rem', 
                              marginTop: '0.5rem',
                              color: darkMode ? colors.neutral.light : colors.neutral.dark
                            }}>
                              <span>By {post.author}</span>
                              <span>{formatDate(post.createdAt || post.publishDate)}</span>
                            </div>
                            
                            {/* Display category tags */}
                            <div style={{ marginTop: '0.5rem' }}>
                              {post.category && (
                                <>
                                  {post.category.split('/').map((cat, index) => (
                                    <CategoryTag 
                                      key={index} 
                                      main={index === 0} 
                                      darkMode={darkMode}
                                    >
                                      {cat}
                                    </CategoryTag>
                                  ))}
                                </>
                              )}
                            </div>
                            
                            <ReadMoreButton 
                              to={`/blog/${post.id}`} 
                              darkMode={darkMode}
                            >
                              Read More
                            </ReadMoreButton>
                          </BlogContent>
                        </BlogCard>
                      </motion.div>
                    )) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        style={{ gridColumn: '1 / -1', textAlign: 'center', padding: spacing.lg }}
                      >
                        <h3>No posts found in this category</h3>
                        <p>Try selecting a different category or check back later for new content.</p>
                      </motion.div>
                    )}
                  </BlogGrid>
                </motion.div>
              </AnimatePresence>
              
              {filteredPosts.length > postsPerPage && (
                <PaginationContainer>
                  <PageButton 
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    darkMode={darkMode}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaChevronLeft />
                  </PageButton>
                  
                  {[...Array(totalPages)].map((_, index) => {
                    const pageNumber = index + 1;
                    const visibleRange = getVisiblePageRange();
                    
                    // Always show first and last page
                    const showFirstLast = pageNumber === 1 || pageNumber === totalPages;
                    
                    // Show pages within the visible range of current page
                    const showInRange = pageNumber >= currentPage - visibleRange && 
                                       pageNumber <= currentPage + visibleRange;
                    
                    // Determine if we should show this page number
                    if (showFirstLast || showInRange) {
                      return (
                        <PageButton
                          key={pageNumber}
                          active={currentPage === pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          darkMode={darkMode}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          {pageNumber}
                        </PageButton>
                      );
                    }
                    
                    // Show ellipsis for skipped pages, but only if there's a gap
                    if (
                      (pageNumber === 2 && currentPage > visibleRange + 2) ||
                      (pageNumber === totalPages - 1 && currentPage < totalPages - visibleRange - 1)
                    ) {
                      return <span key={pageNumber} style={{ alignSelf: 'center' }}>...</span>;
                    }
                    
                    return null;
                  })}
                  
                  <PageButton 
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    darkMode={darkMode}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaChevronRight />
                  </PageButton>
                </PaginationContainer>
              )}
            </>
          )}
        </ContentArea>
      </BlogLayout>
    </BlogContainer>
  );
};

export default Blog;

