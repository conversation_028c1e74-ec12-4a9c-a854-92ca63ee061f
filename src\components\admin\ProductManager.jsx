import { useState, useEffect } from "react";
import {
  collection,
  getDocs,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
} from "firebase/firestore";
import { ref, deleteObject } from "firebase/storage";
import { db, storage, auth } from "../../firebase/config";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import DeleteButton from "../ui/DeleteButton";
import { uploadImage } from "../../utils/fileUpload";
import { useAuth } from "../../context/AuthContext";
import DeleteConfirmationModal from "../ui/DeleteConfirmationModal";
import {
  createVariantOption,
  createVariantCombination,
  generateAllCombinations,
} from "../../utils/productVariantsHelper";

const Container = styled.div`
  padding: ${spacing.lg};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  border-radius: 8px;
  margin-bottom: ${spacing.lg};

  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.md};
  }

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.sm};
  }
`;

const Title = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};

  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const ProductCard = styled.div`
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  padding: ${spacing.md};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const ProductImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: ${spacing.sm};
`;

const Button = styled.button`
  background-color: ${(props) =>
    props.danger
      ? colors.error
      : props.primary
      ? colors.primary.main
      : colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.sm} ${spacing.md};
  margin-right: ${spacing.sm};
  cursor: pointer;
  max-width: 200px;

  &:hover {
    background-color: ${(props) =>
      props.danger
        ? colors.errorDark
        : props.primary
        ? colors.primary.dark
        : colors.secondary.dark};
  }

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs} ${spacing.sm};
    margin-right: 0;
    margin-bottom: ${spacing.xs};
    max-width: 100%;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  padding: ${spacing.md};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  max-width: 100%;

  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.sm};
    gap: ${spacing.sm};
  }
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${spacing.md};
  width: 100%;

  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${spacing.sm};
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;

  &.full-width {
    grid-column: 1 / -1;
  }
`;

const Input = styled.input`
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs};
  }
`;

const TextArea = styled.textarea`
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  min-height: 100px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs};
    min-height: 80px;
  }
`;

const Select = styled.select`
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.xs};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${spacing.sm};
  margin-top: ${spacing.sm};

  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
    width: 100%;
  }
`;

const Message = styled.div`
  padding: ${spacing.sm};
  margin-bottom: ${spacing.md};
  border-radius: 4px;
  background-color: ${(props) =>
    props.type === "success" ? colors.success : colors.error};
  color: white;
`;

const VariantsSection = styled.div`
  margin-top: ${spacing.md};
  padding: ${spacing.md};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)"};
`;

const VariantOptionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.md};
  margin-top: ${spacing.md};
`;

const VariantOption = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.sm};
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
`;

const VariantValues = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${spacing.sm};
  margin-top: ${spacing.xs};
`;

const VariantValue = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.darkMode ? colors.primary.dark : colors.primary.light};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  border-radius: 4px;
  font-size: ${typography.fontSize.sm};
`;

const VariantCombinationsList = styled.div`
  margin-top: ${spacing.md};
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${spacing.md};

  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const VariantCombination = styled.div`
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
`;

const Badge = styled.span`
  display: inline-block;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.secondary.dark : colors.secondary.light};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.secondary.dark};
  font-size: ${typography.fontSize.xs};
  margin-right: ${spacing.xs};
  margin-bottom: ${spacing.xs};
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const PageButton = styled.button`
  background-color: ${(props) =>
    props.active
      ? props.darkMode
        ? colors.primary.dark
        : colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? "white"
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  padding: 5px 10px;
  min-width: 35px;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:hover {
    background-color: ${(props) =>
      props.active
        ? props.darkMode
          ? colors.primary.darker
          : colors.primary.dark
        : props.darkMode
        ? colors.neutral.darker
        : colors.neutral.light};
  }
`;

const ProductManager = ({ darkMode }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: "", type: "" });
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    description: "",
    category: "",
    stock: "",
    featured: false,
    ingredients: "",
    howToUse: "",
    image: null,
    hasVariants: false,
    variantOptions: [],
    variants: [],
  });

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage, setProductsPerPage] = useState(10); // Show 10 products per page in admin view
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Add state for delete confirmation modal
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    product: null,
  });

  // Move useAuth hook to component level - THIS IS THE ONLY CHANGE
  const { isAuthenticated, isAdmin, isEditor } = useAuth();

  // Add resize listener with more granular breakpoints
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Adjust products per page based on screen size with more granular control
      if (window.innerWidth < parseInt(breakpoints.sm)) {
        setProductsPerPage(4); // 4 products on mobile screens
      } else if (window.innerWidth < parseInt(breakpoints.lg)) {
        setProductsPerPage(6); // 6 products on tablet/half desktop screens
      } else {
        setProductsPerPage(10); // 10 products on larger desktop screens
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Set initial value

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Add pagination calculation
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = products.slice(
    indexOfFirstProduct,
    indexOfLastProduct
  );
  const totalPages = Math.ceil(products.length / productsPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      // Use the already declared hook variables instead of calling useAuth() here
      if (!isAuthenticated) {
        throw new Error("You must be logged in to access products");
      }

      if (!isAdmin && !isEditor) {
        throw new Error("You must be an admin or editor to manage products");
      }

      const productsRef = collection(db, "products");
      const snapshot = await getDocs(productsRef);
      const productsList = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      setProducts(productsList);
    } catch (error) {
      console.error("Error fetching products:", error);
      setMessage({
        text: `Error fetching products: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Add a new variant option (e.g., Size, Color, Flavor)
  const [newOptionName, setNewOptionName] = useState("");
  // Store option values in a map keyed by option index
  const [optionValues, setOptionValues] = useState({});

  const addVariantOption = () => {
    if (!newOptionName.trim()) {
      setMessage({ text: "Please enter a variant option name", type: "error" });
      return;
    }

    // Check if option already exists
    const optionExists = formData.variantOptions.some(
      (option) => option.name.toLowerCase() === newOptionName.toLowerCase()
    );

    if (optionExists) {
      setMessage({
        text: `Option "${newOptionName}" already exists`,
        type: "error",
      });
      return;
    }

    // Add new option
    const newOption = createVariantOption(newOptionName, []);
    const updatedOptions = [...formData.variantOptions, newOption];

    setFormData({
      ...formData,
      variantOptions: updatedOptions,
    });

    // Initialize the value for this new option in the optionValues state
    setOptionValues({
      ...optionValues,
      [updatedOptions.length - 1]: "",
    });

    setNewOptionName("");
  };

  // Add a value to a variant option
  const addVariantOptionValue = (optionIndex) => {
    const currentValue = optionValues[optionIndex] || "";

    if (!currentValue.trim()) {
      setMessage({
        text: "Please enter a variant option value",
        type: "error",
      });
      return;
    }

    const option = formData.variantOptions[optionIndex];

    // Check if value already exists for this option
    if (option.values.includes(currentValue)) {
      setMessage({
        text: `Value "${currentValue}" already exists for this option`,
        type: "error",
      });
      return;
    }

    // Add new value to the option
    const updatedOptions = [...formData.variantOptions];
    updatedOptions[optionIndex] = {
      ...option,
      values: [...option.values, currentValue],
    };

    setFormData({
      ...formData,
      variantOptions: updatedOptions,
    });

    // Clear just this option's input value
    setOptionValues({
      ...optionValues,
      [optionIndex]: "",
    });
  };

  // Handle change for a specific option's value input
  const handleOptionValueChange = (optionIndex, value) => {
    setOptionValues({
      ...optionValues,
      [optionIndex]: value,
    });
  };

  // Remove a variant option
  const removeVariantOption = (optionIndex) => {
    const updatedOptions = formData.variantOptions.filter(
      (_, index) => index !== optionIndex
    );
    setFormData({
      ...formData,
      variantOptions: updatedOptions,
    });

    // Remove this option's value from optionValues
    const updatedOptionValues = { ...optionValues };
    delete updatedOptionValues[optionIndex];

    // Remap the keys for the remaining options
    const remappedOptionValues = {};
    updatedOptions.forEach((option, newIndex) => {
      const oldIndex = formData.variantOptions.findIndex(
        (oldOption) => oldOption.name === option.name
      );
      if (oldIndex !== -1 && optionValues[oldIndex]) {
        remappedOptionValues[newIndex] = optionValues[oldIndex];
      }
    });

    setOptionValues(remappedOptionValues);

    // Regenerate variants if needed
    if (formData.variants.length > 0) {
      generateVariants(updatedOptions);
    }
  };

  // Remove a value from a variant option
  const removeVariantOptionValue = (optionIndex, valueIndex) => {
    const option = formData.variantOptions[optionIndex];
    const updatedValues = option.values.filter(
      (_, index) => index !== valueIndex
    );

    const updatedOptions = [...formData.variantOptions];
    updatedOptions[optionIndex] = {
      ...option,
      values: updatedValues,
    };

    setFormData({
      ...formData,
      variantOptions: updatedOptions,
    });

    // Regenerate variants if needed
    if (formData.variants.length > 0) {
      generateVariants(updatedOptions);
    }
  };

  // Generate all possible variant combinations
  const generateVariants = (options = formData.variantOptions) => {
    if (options.length === 0) {
      setFormData({
        ...formData,
        variants: [],
      });
      return;
    }

    // Generate all possible combinations
    const combinations = generateAllCombinations(options);

    // Preserve existing variant data if possible
    const updatedVariants = combinations.map((newVariant) => {
      // Try to find an existing variant with the same options
      const existingVariant = formData.variants.find((variant) => {
        return Object.keys(newVariant.options).every(
          (key) => variant.options[key] === newVariant.options[key]
        );
      });

      if (existingVariant) {
        return existingVariant;
      }

      return newVariant;
    });

    setFormData({
      ...formData,
      variants: updatedVariants,
    });
  };

  // Update a specific variant's data
  const updateVariant = (variantIndex, field, value) => {
    const updatedVariants = [...formData.variants];
    updatedVariants[variantIndex] = {
      ...updatedVariants[variantIndex],
      [field]:
        field === "price" || field === "stock" ? parseFloat(value) : value,
    };

    setFormData({
      ...formData,
      variants: updatedVariants,
    });
  };

  // Handle variant image upload
  const handleVariantImageChange = async (variantIndex, e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setLoading(true);
      const imageUrl = await uploadImage(
        file,
        `products/variants/${formData.name
          .replace(/\s+/g, "_")
          .toLowerCase()}_variant_${variantIndex}_primary`
      );

      // Update the variant with the image URL
      const updatedVariants = [...formData.variants];
      const currentVariant = updatedVariants[variantIndex];

      // Check if the current primary image exists in the media array
      const primaryImageInMedia = currentVariant.media?.find(
        (item) => item.url === currentVariant.imageUrl
      );

      // If the primary image is not in the media array, we need to add it
      if (currentVariant.imageUrl && !primaryImageInMedia) {
        // Create a media array if it doesn't exist
        if (!currentVariant.media) {
          currentVariant.media = [];
        }

        // Add the old primary image to the media array
        currentVariant.media.unshift({
          url: currentVariant.imageUrl,
          type: "image",
          alt: "Previous Primary Image",
          isPrimary: false,
        });
      }

      // Update the variant with the new primary image
      updatedVariants[variantIndex] = {
        ...currentVariant,
        imageUrl: imageUrl,
      };

      setFormData({
        ...formData,
        variants: updatedVariants,
      });

      setLoading(false);
      setMessage({
        text: "Variant primary image uploaded successfully",
        type: "success",
      });
    } catch (error) {
      setLoading(false);
      setMessage({
        text: `Error uploading variant image: ${error.message}`,
        type: "error",
      });
    }
  };

  // Handle variant media upload
  const handleVariantMediaUpload = async (variantIndex, e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setLoading(true);
    setMessage({ text: "Uploading variant media files...", type: "info" });

    try {
      const variant = formData.variants[variantIndex];
      const variantMedia = variant.media || [];

      const uploadPromises = files.map(async (file) => {
        // Determine media type
        const fileType = file.type.split("/")[0]; // 'image', 'video', etc.
        const fileExtension = file.name.split(".").pop().toLowerCase();
        let mediaType = "image";

        // More comprehensive video type detection
        if (
          fileType === "video" ||
          ["mp4", "webm", "ogg", "mov", "avi", "wmv", "flv", "mkv"].includes(
            fileExtension
          )
        ) {
          mediaType = "video";
        } else if (fileExtension === "gif") {
          mediaType = "gif";
        }

        // Check limits based on media type
        const currentMediaCount = variantMedia.filter(
          (item) => item.type === mediaType
        ).length;
        const maxCount = {
          image: 15,
          gif: 2,
          video: 3,
        };

        if (currentMediaCount >= maxCount[mediaType]) {
          throw new Error(
            `Maximum ${maxCount[mediaType]} ${mediaType}s allowed for this variant`
          );
        }

        // Upload the file
        const variantName = Object.values(variant.options).join("_");
        const path = `products/variants/${formData.name
          .replace(/\s+/g, "_")
          .toLowerCase()}_${variantName}_${mediaType}_${Date.now()}`;
        const url = await uploadImage(file, path);

        return {
          url,
          type: mediaType,
          alt: file.name,
          muted: mediaType === "video", // Default videos to muted
        };
      });

      const newMediaItems = await Promise.all(uploadPromises);

      // Update the variant with the new media
      const updatedVariants = [...formData.variants];
      const currentVariant = updatedVariants[variantIndex];

      // Mark all new media items as not primary
      const markedMediaItems = newMediaItems.map((item) => ({
        ...item,
        isPrimary: false,
      }));

      // Ensure the primary image is not duplicated in the media array
      const filteredExistingMedia = currentVariant.media
        ? currentVariant.media.filter(
            (item) => item.url !== currentVariant.imageUrl
          )
        : [];

      updatedVariants[variantIndex] = {
        ...currentVariant,
        media: [...filteredExistingMedia, ...markedMediaItems],
      };

      setFormData({
        ...formData,
        variants: updatedVariants,
      });

      setMessage({
        text: "Variant media uploaded successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Error uploading variant media:", error);
      setMessage({
        text: `Error uploading variant media: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a variant media item
  const handleRemoveVariantMedia = (variantIndex, mediaIndex) => {
    const updatedVariants = [...formData.variants];
    const variantMedia = [...(updatedVariants[variantIndex].media || [])];

    variantMedia.splice(mediaIndex, 1);

    updatedVariants[variantIndex] = {
      ...updatedVariants[variantIndex],
      media: variantMedia,
    };

    setFormData({
      ...formData,
      variants: updatedVariants,
    });
  };

  const handleImageChange = (e) => {
    if (e.target.files[0]) {
      setFormData({
        ...formData,
        image: e.target.files[0],
      });
    }
  };

  // Handle media upload for product
  const handleMediaUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setLoading(true);
    setMessage({ text: "Uploading media files...", type: "info" });

    try {
      const uploadPromises = files.map(async (file) => {
        // Determine media type
        const fileType = file.type.split("/")[0]; // 'image', 'video', etc.
        const fileExtension = file.name.split(".").pop().toLowerCase();
        let mediaType = "image";

        // More comprehensive video type detection
        if (
          fileType === "video" ||
          ["mp4", "webm", "ogg", "mov", "avi", "wmv", "flv", "mkv"].includes(
            fileExtension
          )
        ) {
          mediaType = "video";
        } else if (fileExtension === "gif") {
          mediaType = "gif";
        }

        // Check limits based on media type
        const currentMediaCount = formData.media.filter(
          (item) => item.type === mediaType
        ).length;
        const maxCount = {
          image: 15,
          gif: 2,
          video: 3,
        };

        if (currentMediaCount >= maxCount[mediaType]) {
          throw new Error(
            `Maximum ${maxCount[mediaType]} ${mediaType}s allowed`
          );
        }

        // Upload the file
        const path = `products/${formData.name
          .replace(/\s+/g, "_")
          .toLowerCase()}_${mediaType}_${Date.now()}`;
        const url = await uploadImage(file, path);

        return {
          url,
          type: mediaType,
          alt: file.name,
          muted: mediaType === "video", // Default videos to muted
        };
      });

      const newMediaItems = await Promise.all(uploadPromises);

      setFormData({
        ...formData,
        media: [...formData.media, ...newMediaItems],
      });

      setMessage({ text: "Media uploaded successfully", type: "success" });
    } catch (error) {
      console.error("Error uploading media:", error);
      setMessage({
        text: `Error uploading media: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a media item
  const handleRemoveMedia = (index) => {
    const updatedMedia = [...formData.media];
    updatedMedia.splice(index, 1);

    setFormData({
      ...formData,
      media: updatedMedia,
    });
  };

  const resetForm = () => {
    setFormData({
      name: "",
      price: "",
      description: "",
      category: "",
      stock: "",
      featured: false,
      ingredients: "",
      howToUse: "",
      image: null,
      media: [], // Array of media items (images, GIFs, videos)
      hasVariants: false,
      variantOptions: [],
      variants: [],
    });
    setEditingProduct(null);
  };

  const handleAddProduct = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ text: "", type: "" });

    try {
      // Validate form data
      if (
        !formData.name ||
        !formData.price ||
        !formData.description ||
        !formData.category
      ) {
        throw new Error("Please fill in all required fields");
      }

      let imageUrl = "";
      if (formData.image) {
        imageUrl = await uploadImage(formData.image);
      }

      // Add new product to Firestore
      const productData = {
        name: formData.name,
        price: parseFloat(formData.price),
        description: formData.description,
        category: formData.category,
        stock: parseInt(formData.stock) || 0,
        featured: formData.featured,
        ingredients: formData.ingredients,
        howToUse: formData.howToUse,
        imageUrl: imageUrl,
        media: formData.media || [], // Include media array
        hasVariants: formData.hasVariants,
        variantOptions: formData.hasVariants ? formData.variantOptions : [],
        variants: formData.hasVariants ? formData.variants : [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await addDoc(collection(db, "products"), productData);

      setMessage({ text: "Product added successfully!", type: "success" });
      resetForm();
      setShowForm(false);
      fetchProducts();
    } catch (error) {
      console.error("Error adding product:", error);
      setMessage({
        text: `Error adding product: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditProduct = (product) => {
    setFormData({
      name: product.name,
      price: product.price.toString(),
      description: product.description,
      category: product.category,
      stock: product.stock.toString(),
      featured: product.featured || false,
      ingredients: product.ingredients || "",
      howToUse: product.howToUse || "",
      image: null,
      media: product.media || [], // Array of media items (images, GIFs, videos)
      hasVariants: product.hasVariants || false,
      variantOptions: product.variantOptions || [],
      variants: product.variants || [],
    });
    setEditingProduct(product);
    setShowForm(true);
  };

  const handleUpdateProduct = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ text: "", type: "" });

    try {
      // Validate form data
      if (
        !formData.name ||
        !formData.price ||
        !formData.description ||
        !formData.category
      ) {
        throw new Error("Please fill in all required fields");
      }

      let imageUrl = editingProduct.imageUrl;
      if (formData.image) {
        // Delete old image if exists
        if (editingProduct.imageUrl) {
          try {
            const oldImageRef = ref(storage, editingProduct.imageUrl);
            await deleteObject(oldImageRef);
          } catch (error) {
            console.error("Error deleting old image:", error);
          }
        }

        imageUrl = await uploadImage(formData.image);
      }

      // Update product in Firestore
      const productData = {
        name: formData.name,
        price: parseFloat(formData.price),
        description: formData.description,
        category: formData.category,
        stock: parseInt(formData.stock) || 0,
        featured: formData.featured,
        ingredients: formData.ingredients,
        howToUse: formData.howToUse,
        imageUrl: imageUrl,
        media: formData.media || [], // Include media array
        hasVariants: formData.hasVariants,
        variantOptions: formData.hasVariants ? formData.variantOptions : [],
        variants: formData.hasVariants ? formData.variants : [],
        updatedAt: new Date(),
      };

      await updateDoc(doc(db, "products", editingProduct.id), productData);

      setMessage({ text: "Product updated successfully!", type: "success" });
      resetForm();
      setShowForm(false);
      fetchProducts();
    } catch (error) {
      console.error("Error updating product:", error);
      setMessage({
        text: `Error updating product: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProduct = async (product) => {
    setDeleteModal({
      isOpen: true,
      product,
    });
  };

  const confirmDeleteProduct = async () => {
    const product = deleteModal.product;
    setLoading(true);
    try {
      // Delete image from storage if exists
      if (product.imageUrl) {
        try {
          const imageRef = ref(storage, product.imageUrl);
          await deleteObject(imageRef);
        } catch (error) {
          console.error("Error deleting image:", error);
        }
      }

      // Delete product from Firestore
      await deleteDoc(doc(db, "products", product.id));

      setMessage({ text: "Product deleted successfully!", type: "success" });
      fetchProducts();
    } catch (error) {
      console.error("Error deleting product:", error);
      setMessage({
        text: `Error deleting product: ${error.message}`,
        type: "error",
      });
    } finally {
      setLoading(false);
      setDeleteModal({ isOpen: false, product: null });
    }
  };

  return (
    <Container darkMode={darkMode}>
      <Title darkMode={darkMode}>Product Management</Title>

      {message.text && <Message type={message.type}>{message.text}</Message>}

      <Button
        primary
        onClick={() => {
          resetForm();
          setShowForm(!showForm);
        }}
      >
        {showForm ? "Cancel" : "Add New Product"}
      </Button>

      {showForm && (
        <Form
          onSubmit={editingProduct ? handleUpdateProduct : handleAddProduct}
          darkMode={darkMode}
        >
          <FormGroup darkMode={darkMode}>
            <label>Product Name*</label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup darkMode={darkMode}>
            <label>Price*</label>
            <Input
              type="number"
              name="price"
              value={formData.price}
              onChange={handleInputChange}
              step="0.01"
              min="0"
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup className="full-width" darkMode={darkMode}>
            <label>Description*</label>
            <TextArea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup className="full-width" darkMode={darkMode}>
            <label>Ingredients</label>
            <TextArea
              name="ingredients"
              value={formData.ingredients}
              onChange={handleInputChange}
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup className="full-width" darkMode={darkMode}>
            <label>How to Use</label>
            <TextArea
              name="howToUse"
              value={formData.howToUse}
              onChange={handleInputChange}
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup darkMode={darkMode}>
            <label>Category*</label>
            <Select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              required
              darkMode={darkMode}
            >
              <option value="">Select a category</option>
              <option value="face">Face Care</option>
              <option value="body">Body Care</option>
              <option value="hair">Hair Care</option>
              <option value="lips">Lip Care</option>
              <option value="sets">Gift Sets</option>
            </Select>
          </FormGroup>

          <FormGroup darkMode={darkMode}>
            <label>Stock*</label>
            <Input
              type="number"
              name="stock"
              value={formData.stock}
              onChange={handleInputChange}
              min="0"
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup darkMode={darkMode}>
            <label style={{ display: "flex", alignItems: "center" }}>
              <Input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                style={{ marginRight: spacing.xs, width: "auto" }}
              />
              Featured Product
            </label>
          </FormGroup>

          <FormGroup className="full-width" darkMode={darkMode}>
            <label>
              Primary Product Image (Used in product listings and cart)
            </label>
            <Input type="file" accept="image/*" onChange={handleImageChange} />
            {editingProduct && editingProduct.imageUrl && (
              <p>Current image: {editingProduct.imageUrl.split("/").pop()}</p>
            )}
          </FormGroup>

          <FormGroup className="full-width" darkMode={darkMode}>
            <label>Product Media Gallery (Images, GIFs, Videos)</label>
            <p
              style={{
                fontSize: typography.fontSize.sm,
                marginBottom: spacing.sm,
              }}
            >
              You can upload up to 15 images, 2 GIFs, and 3 videos per product.
            </p>
            <Input
              type="file"
              accept="image/*,video/*"
              multiple
              onChange={handleMediaUpload}
            />

            {formData.media && formData.media.length > 0 && (
              <div style={{ marginTop: spacing.md }}>
                <h4>Current Media ({formData.media.length})</h4>
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns:
                      "repeat(auto-fill, minmax(100px, 1fr))",
                    gap: spacing.sm,
                    marginTop: spacing.sm,
                  }}
                >
                  {formData.media.map((item, index) => (
                    <div
                      key={index}
                      style={{
                        position: "relative",
                        border: `1px solid ${
                          darkMode ? colors.neutral.gray : colors.neutral.light
                        }`,
                        borderRadius: "4px",
                        padding: spacing.xs,
                        backgroundColor: darkMode
                          ? colors.neutral.darker
                          : colors.neutral.white,
                      }}
                    >
                      {item.type === "video" ? (
                        <video
                          src={item.url}
                          style={{
                            width: "100%",
                            height: "80px",
                            objectFit: "cover",
                          }}
                          muted
                        />
                      ) : (
                        <img
                          src={item.url}
                          alt={item.alt || `Media ${index}`}
                          style={{
                            width: "100%",
                            height: "80px",
                            objectFit: "cover",
                          }}
                        />
                      )}
                      <div
                        style={{
                          marginTop: spacing.xs,
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <small style={{ fontSize: typography.fontSize.xs }}>
                          {item.type.toUpperCase()}
                        </small>
                        <button
                          type="button"
                          onClick={() => handleRemoveMedia(index)}
                          style={{
                            background: "none",
                            border: "none",
                            color: colors.error,
                            cursor: "pointer",
                            padding: "2px",
                            fontSize: typography.fontSize.sm,
                          }}
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </FormGroup>

          {/* Product Variants Section */}
          <FormGroup darkMode={darkMode}>
            <label style={{ display: "flex", alignItems: "center" }}>
              <Input
                type="checkbox"
                name="hasVariants"
                checked={formData.hasVariants}
                onChange={handleInputChange}
                style={{ marginRight: spacing.xs, width: "auto" }}
              />
              This product has variants (sizes, colors, flavors, etc.)
            </label>
          </FormGroup>

          {formData.hasVariants && (
            <VariantsSection darkMode={darkMode}>
              <h3>Product Variants</h3>

              {/* Add new variant option */}
              <FormGroup darkMode={darkMode}>
                <label>Add Variant Option (e.g., Size, Color, Flavor)</label>
                <div style={{ display: "flex", gap: spacing.sm }}>
                  <Input
                    type="text"
                    value={newOptionName}
                    onChange={(e) => setNewOptionName(e.target.value)}
                    placeholder="Option name"
                    darkMode={darkMode}
                  />
                  <Button onClick={addVariantOption} type="button">
                    Add Option
                  </Button>
                </div>
              </FormGroup>

              {/* Display existing variant options */}
              {formData.variantOptions.length > 0 && (
                <VariantOptionsList>
                  {formData.variantOptions.map((option, optionIndex) => (
                    <VariantOption key={optionIndex} darkMode={darkMode}>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <h4>{option.name}</h4>
                        <Button
                          onClick={() => removeVariantOption(optionIndex)}
                          type="button"
                          danger
                        >
                          Remove
                        </Button>
                      </div>

                      {/* Add values to this option */}
                      <div
                        style={{
                          display: "flex",
                          gap: spacing.sm,
                          marginTop: spacing.sm,
                        }}
                      >
                        <Input
                          type="text"
                          value={optionValues[optionIndex] || ""}
                          onChange={(e) =>
                            handleOptionValueChange(optionIndex, e.target.value)
                          }
                          placeholder={`Add ${option.name} value`}
                          darkMode={darkMode}
                        />
                        <Button
                          onClick={() => addVariantOptionValue(optionIndex)}
                          type="button"
                        >
                          Add
                        </Button>
                      </div>

                      {/* Display values for this option */}
                      {option.values.length > 0 && (
                        <VariantValues>
                          {option.values.map((value, valueIndex) => (
                            <VariantValue key={valueIndex} darkMode={darkMode}>
                              {value}
                              <span
                                style={{
                                  cursor: "pointer",
                                  marginLeft: spacing.xs,
                                }}
                                onClick={() =>
                                  removeVariantOptionValue(
                                    optionIndex,
                                    valueIndex
                                  )
                                }
                              >
                                ×
                              </span>
                            </VariantValue>
                          ))}
                        </VariantValues>
                      )}
                    </VariantOption>
                  ))}
                </VariantOptionsList>
              )}

              {/* Generate variants button */}
              {formData.variantOptions.length > 0 &&
                formData.variantOptions.every(
                  (option) => option.values.length > 0
                ) && (
                  <div style={{ marginTop: spacing.md }}>
                    <Button
                      onClick={() => generateVariants()}
                      type="button"
                      primary
                    >
                      Generate Variant Combinations
                    </Button>
                  </div>
                )}

              {/* Display variant combinations */}
              {formData.variants.length > 0 && (
                <>
                  <h3 style={{ marginTop: spacing.lg }}>
                    Variant Combinations
                  </h3>
                  <p>
                    Set price adjustments and stock levels for each variant
                    combination:
                  </p>

                  <VariantCombinationsList>
                    {formData.variants.map((variant, variantIndex) => (
                      <VariantCombination
                        key={variantIndex}
                        darkMode={darkMode}
                      >
                        <div style={{ marginBottom: spacing.sm }}>
                          {Object.entries(variant.options).map(
                            ([name, value]) => (
                              <Badge key={name} darkMode={darkMode}>
                                {name}: {value}
                              </Badge>
                            )
                          )}
                        </div>

                        <FormGroup darkMode={darkMode}>
                          <label>Price Adjustment</label>
                          <Input
                            type="number"
                            value={variant.price || 0}
                            onChange={(e) =>
                              updateVariant(
                                variantIndex,
                                "price",
                                e.target.value
                              )
                            }
                            step="0.01"
                            placeholder="0.00"
                            darkMode={darkMode}
                          />
                          <small>
                            Additional cost for this variant (can be negative)
                          </small>
                        </FormGroup>

                        <FormGroup darkMode={darkMode}>
                          <label>Stock</label>
                          <Input
                            type="number"
                            value={variant.stock || 0}
                            onChange={(e) =>
                              updateVariant(
                                variantIndex,
                                "stock",
                                e.target.value
                              )
                            }
                            min="0"
                            darkMode={darkMode}
                          />
                        </FormGroup>

                        <FormGroup darkMode={darkMode}>
                          <label>SKU (Optional)</label>
                          <Input
                            type="text"
                            value={variant.sku || ""}
                            onChange={(e) =>
                              updateVariant(variantIndex, "sku", e.target.value)
                            }
                            placeholder="SKU"
                            darkMode={darkMode}
                          />
                        </FormGroup>

                        <FormGroup darkMode={darkMode}>
                          <label>Primary Variant Image (Optional)</label>
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={(e) =>
                              handleVariantImageChange(variantIndex, e)
                            }
                          />
                          {variant.imageUrl && (
                            <div style={{ marginTop: spacing.xs }}>
                              <img
                                src={variant.imageUrl}
                                alt={`Variant ${variantIndex}`}
                                style={{
                                  width: "60px",
                                  height: "60px",
                                  objectFit: "cover",
                                  borderRadius: "4px",
                                  marginTop: spacing.xs,
                                }}
                              />
                            </div>
                          )}
                        </FormGroup>

                        <FormGroup darkMode={darkMode}>
                          <label>Variant Media Gallery (Optional)</label>
                          <p
                            style={{
                              fontSize: typography.fontSize.xs,
                              marginBottom: spacing.sm,
                            }}
                          >
                            You can upload up to 15 images, 2 GIFs, and 3 videos
                            per variant.
                          </p>
                          <Input
                            type="file"
                            accept="image/*,video/*"
                            multiple
                            onChange={(e) =>
                              handleVariantMediaUpload(variantIndex, e)
                            }
                          />

                          {variant.media && variant.media.length > 0 && (
                            <div style={{ marginTop: spacing.md }}>
                              <h5>Variant Media ({variant.media.length})</h5>
                              <div
                                style={{
                                  display: "grid",
                                  gridTemplateColumns:
                                    "repeat(auto-fill, minmax(80px, 1fr))",
                                  gap: spacing.xs,
                                  marginTop: spacing.sm,
                                }}
                              >
                                {variant.media.map((item, mediaIndex) => (
                                  <div
                                    key={mediaIndex}
                                    style={{
                                      position: "relative",
                                      border: `1px solid ${
                                        darkMode
                                          ? colors.neutral.gray
                                          : colors.neutral.light
                                      }`,
                                      borderRadius: "4px",
                                      padding: spacing.xs,
                                      backgroundColor: darkMode
                                        ? colors.neutral.darker
                                        : colors.neutral.white,
                                    }}
                                  >
                                    {item.type === "video" ? (
                                      <video
                                        src={item.url}
                                        style={{
                                          width: "100%",
                                          height: "60px",
                                          objectFit: "cover",
                                        }}
                                        muted
                                      />
                                    ) : (
                                      <img
                                        src={item.url}
                                        alt={item.alt || `Media ${mediaIndex}`}
                                        style={{
                                          width: "100%",
                                          height: "60px",
                                          objectFit: "cover",
                                        }}
                                      />
                                    )}
                                    <div
                                      style={{
                                        marginTop: spacing.xs,
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                      }}
                                    >
                                      <small
                                        style={{
                                          fontSize: typography.fontSize.xs,
                                        }}
                                      >
                                        {item.type.toUpperCase()}
                                      </small>
                                      <button
                                        type="button"
                                        onClick={() =>
                                          handleRemoveVariantMedia(
                                            variantIndex,
                                            mediaIndex
                                          )
                                        }
                                        style={{
                                          background: "none",
                                          border: "none",
                                          color: colors.error,
                                          cursor: "pointer",
                                          padding: "2px",
                                          fontSize: typography.fontSize.sm,
                                        }}
                                      >
                                        ×
                                      </button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </FormGroup>
                      </VariantCombination>
                    ))}
                  </VariantCombinationsList>
                </>
              )}
            </VariantsSection>
          )}

          <FormGroup className="full-width" darkMode={darkMode}>
            <Button primary type="submit" disabled={loading}>
              {loading
                ? "Processing..."
                : editingProduct
                ? "Update Product"
                : "Add Product"}
            </Button>
          </FormGroup>
        </Form>
      )}

      {loading && !showForm ? (
        <p>Loading products...</p>
      ) : (
        <>
          <h3>Product List ({products.length})</h3>
          <ProductGrid>
            {currentProducts.map((product) => (
              <ProductCard key={product.id} darkMode={darkMode}>
                {product.imageUrl && (
                  <ProductImage src={product.imageUrl} alt={product.name} />
                )}
                <h4>{product.name}</h4>
                <p>${product.price.toFixed(2)}</p>
                <p>Category: {product.category}</p>
                <p>Stock: {product.stock}</p>
                {product.hasVariants && (
                  <div style={{ marginTop: spacing.xs }}>
                    <p
                      style={{
                        color: colors.secondary.main,
                        fontWeight: "bold",
                      }}
                    >
                      Has Variants: {product.variantOptions?.length || 0}{" "}
                      options
                    </p>
                    {product.variantOptions?.map((option, index) => (
                      <small key={index} style={{ display: "block" }}>
                        {option.name}: {option.values.join(", ")}
                      </small>
                    ))}
                  </div>
                )}
                <ButtonGroup>
                  <Button onClick={() => handleEditProduct(product)}>
                    Edit
                  </Button>
                  <DeleteButton onClick={() => handleDeleteProduct(product)}>
                    Delete
                  </DeleteButton>
                </ButtonGroup>
              </ProductCard>
            ))}
          </ProductGrid>

          {/* Add pagination */}
          {products.length > productsPerPage && (
            <PaginationContainer>
              <PageButton
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                darkMode={darkMode}
              >
                &lt;
              </PageButton>

              {[...Array(totalPages)].map((_, index) => {
                const pageNumber = index + 1;

                // Show first, last, current and adjacent pages
                if (
                  pageNumber === 1 ||
                  pageNumber === totalPages ||
                  (pageNumber >= currentPage - 1 &&
                    pageNumber <= currentPage + 1)
                ) {
                  return (
                    <PageButton
                      key={pageNumber}
                      active={currentPage === pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      darkMode={darkMode}
                    >
                      {pageNumber}
                    </PageButton>
                  );
                }

                // Show ellipsis
                if (
                  (pageNumber === 2 && currentPage > 3) ||
                  (pageNumber === totalPages - 1 &&
                    currentPage < totalPages - 2)
                ) {
                  return (
                    <span key={pageNumber} style={{ alignSelf: "center" }}>
                      ...
                    </span>
                  );
                }

                return null;
              })}

              <PageButton
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                darkMode={darkMode}
              >
                &gt;
              </PageButton>
            </PaginationContainer>
          )}
        </>
      )}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, product: null })}
        onConfirm={confirmDeleteProduct}
        itemName={deleteModal.product?.name || ""}
        darkMode={darkMode}
      />
    </Container>
  );
};

export default ProductManager;
