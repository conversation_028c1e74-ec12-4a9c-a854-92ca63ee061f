import React, { useState, useCallback } from "react";
import styled from "styled-components";
import { colors, spacing, typography } from "../../styles";
import ReviewForm from "./ReviewForm";
import ReviewList from "./ReviewList";

const Container = styled.div`
  margin: ${spacing.xl} 0;
  padding-top: ${spacing.lg};
  border-top: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
`;

const Title = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  margin-bottom: ${spacing.lg};
`;

const Tab = styled.button`
  padding: ${spacing.sm} ${spacing.md};
  background: none;
  border: none;
  border-bottom: 3px solid
    ${(props) => (props.active ? colors.primary.main : "transparent")};
  color: ${(props) =>
    props.active
      ? props.darkMode
        ? colors.primary.light
        : colors.primary.main
      : props.darkMode
      ? colors.neutral.light
      : colors.neutral.dark};
  font-weight: ${(props) =>
    props.active ? typography.fontWeight.bold : typography.fontWeight.regular};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: ${(props) =>
      props.darkMode ? colors.primary.light : colors.primary.main};
  }
`;

/**
 * Reviews Component
 * @param {Object} props
 * @param {string} props.productId - ID of the product
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {function} props.onRatingUpdate - Callback when rating is updated
 * @returns {JSX.Element}
 */
const Reviews = ({ productId, darkMode, onRatingUpdate }) => {
  const [activeTab, setActiveTab] = useState("reviews");
  const [refreshKey, setRefreshKey] = useState(0);

  // Handle review added
  const handleReviewAdded = () => {
    // Force refresh of review list
    setRefreshKey((prevKey) => prevKey + 1);
    // Switch to reviews tab
    setActiveTab("reviews");
  };

  // Handle reviews loaded - memoize the callback to prevent unnecessary re-renders
  const handleReviewsLoaded = useCallback(
    (ratingData) => {
      if (onRatingUpdate) {
        onRatingUpdate(ratingData);
      }
    },
    [onRatingUpdate]
  );

  return (
    <Container darkMode={darkMode}>
      <Title darkMode={darkMode}>Reviews & Ratings</Title>

      <TabsContainer darkMode={darkMode}>
        <Tab
          active={activeTab === "reviews"}
          onClick={() => setActiveTab("reviews")}
          darkMode={darkMode}
        >
          Read Reviews
        </Tab>
        <Tab
          active={activeTab === "write"}
          onClick={() => setActiveTab("write")}
          darkMode={darkMode}
        >
          Write a Review
        </Tab>
      </TabsContainer>

      {activeTab === "reviews" ? (
        <ReviewList
          key={refreshKey}
          productId={productId}
          darkMode={darkMode}
          onReviewsLoaded={handleReviewsLoaded}
        />
      ) : (
        <ReviewForm
          productId={productId}
          onReviewAdded={handleReviewAdded}
          darkMode={darkMode}
        />
      )}
    </Container>
  );
};

export default Reviews;
