import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, limit } from 'firebase/firestore';
import { db } from '../../firebase/config';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { Link } from 'react-router-dom';
import ProductCard from '../products/ProductCard';

const SectionContainer = styled.section`
  padding: ${spacing['3xl']} ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Title = styled(motion.h2)`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.sm};
  font-family: ${typography.fontFamily.secondary};
  font-size: ${typography.fontSize['2xl']};
`;

const Subtitle = styled(motion.p)`
  color: ${props => props.darkMode ? colors.neutral.lighter : colors.neutral.dark};
  max-width: 600px;
  margin: 0 auto;
  font-size: ${typography.fontSize.md};
`;

const ProductsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: ${spacing.lg};
  margin-top: ${spacing.xl};
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const ViewAllLink = styled(motion.div)`
  text-align: center;
  margin-top: ${spacing.xl};
`;

const FeaturedProducts = ({ darkMode }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        const q = query(
          collection(db, 'products'),
          where('featured', '==', true),
          limit(4)
        );
        
        const querySnapshot = await getDocs(q);
        const featuredProducts = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        setProducts(featuredProducts);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching featured products:', err);
        setError('Failed to load featured products. Please try again later.');
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <SectionContainer darkMode={darkMode}>
        <SectionHeader>
          <Title darkMode={darkMode}>Featured Products</Title>
          <Subtitle darkMode={darkMode}>Loading our premium selections...</Subtitle>
        </SectionHeader>
      </SectionContainer>
    );
  }

  if (error) {
    return (
      <SectionContainer darkMode={darkMode}>
        <SectionHeader>
          <Title darkMode={darkMode}>Featured Products</Title>
          <Subtitle darkMode={darkMode}>{error}</Subtitle>
        </SectionHeader>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer darkMode={darkMode}>
      <SectionHeader>
        <Title 
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Our Featured Collection
        </Title>
        <Subtitle 
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Discover our most loved products, handcrafted with premium natural ingredients
        </Subtitle>
      </SectionHeader>
      
      {products.length > 0 ? (
        <>
          <ProductsGrid
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {products.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </ProductsGrid>
          
          <ViewAllLink
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Link 
              to="/shop" 
              style={{ 
                color: darkMode ? colors.primary.light : colors.primary.dark,
                fontSize: typography.fontSize.lg,
                fontWeight: 500,
                textDecoration: 'none',
                display: 'inline-block',
                padding: spacing.sm,
                borderBottom: `2px solid ${darkMode ? colors.primary.light : colors.primary.dark}`
              }}
            >
              View All Products →
            </Link>
          </ViewAllLink>
        </>
      ) : (
        <Subtitle darkMode={darkMode} style={{ textAlign: 'center', marginTop: spacing.xl }}>
          No featured products available at the moment. Check back soon!
        </Subtitle>
      )}
    </SectionContainer>
  );
};

export default FeaturedProducts;
