<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Email System</h1>
        <p>This will test your email logging system (SendGrid integration coming next!)</p>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="to">To Email:</label>
                <input type="email" id="to" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" required placeholder="Test Email Subject">
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" required placeholder="Your test message here..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="orderReference">Order Reference (optional):</label>
                <input type="text" id="orderReference" placeholder="ORDER_123">
            </div>
            
            <button type="submit">Send Test Email</button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDOJPnJKJJKJJKJJKJJKJJKJJKJJKJKJKJ", // Replace with your actual config
            authDomain: "skinglow1000.firebaseapp.com",
            projectId: "skinglow1000",
            storageBucket: "skinglow1000.appspot.com",
            messagingSenderId: "502278853344",
            appId: "1:502278853344:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app, 'us-central1');

        // Test email function
        const sendCustomEmail = httpsCallable(functions, 'sendCustomEmail');

        document.getElementById('emailForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            
            try {
                // You'll need to sign in as an admin first
                // For testing, you can temporarily modify the function to not require admin
                
                const formData = {
                    to: document.getElementById('to').value,
                    subject: document.getElementById('subject').value,
                    message: document.getElementById('message').value,
                    orderReference: document.getElementById('orderReference').value
                };
                
                const result = await sendCustomEmail(formData);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ ${result.data.message}`;
                resultDiv.style.display = 'block';
                
                // Clear form
                document.getElementById('emailForm').reset();
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });
    </script>
</body>
</html>
