import styled from 'styled-components';
import { colors, breakpoints } from '../../styles';

export const SearchContainer = styled.div`
position: relative;
  width: 100%;
  margin-bottom: 1rem;
`;

export const SearchInputWrapper = styled.div`
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background-color: ${props => props.darkMode ? colors.neutral.darkGray : colors.neutral.white};
  border: 2px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.lightGray};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover, &:focus-within {
    border-color: ${colors.primary.main};
    box-shadow: 0 0 8px ${colors.primary.light};
  }
`;

export const SearchInput = styled.input`
  flex: 1;
  border: none;
  background: transparent;
  padding: 0.5rem;
  margin: 0 0.5rem;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  font-size: 1rem;
  font-family: inherit;
  
  &:focus {
    outline: none;
  }
  
  &::placeholder {
    color: ${props => props.darkMode ? colors.neutral.gray : colors.neutral.darkGray};
    font-style: italic;
  }
`;

export const SearchButton = styled.button`
  background: transparent;
  border: none;
  cursor: pointer;
  color: ${props => props.darkMode ? colors.neutral.gray : colors.neutral.darkGray};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
  
  &:hover {
    color: ${colors.primary.main};
  }
`;

export const ResultsContainer = styled.div`
  position: relative;
  width: 50%;
  left: 0;
  right: 0;
  max-height: 139px;
  overflow: auto;
  background-color: ${props => props.darkMode ? colors.neutral.darkGray : colors.neutral.white};
  border: 2px solid ${colors.primary.light};
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000; 
  display: block; 
 
  @media (max-width: ${breakpoints.md}) {
    position: realtive;
    top: auto;
    width: 75%;
    left: 25%;
    right: 0%;
    margin-top: 5px;
    border: 2px solid ${colors.primary.light};
    border-radius: 8px;

`;

export const ResultItem = styled.div`
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.lightGray};
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: ${colors.primary.light};
  }
`;

export const ResultTitle = styled.div`
  font-weight: 500;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

export const ResultType = styled.div`
  font-size: 0.8rem;
  color: ${props => props.darkMode ? colors.neutral.lightGray : colors.neutral.darkGray};
  text-transform: capitalize;
`;

export const NoResults = styled.div`
  padding: 1rem;
  text-align: center;
  color: ${props => props.darkMode ? colors.neutral.lightGray : colors.neutral.darkGray};
  font-style: italic;
`;






