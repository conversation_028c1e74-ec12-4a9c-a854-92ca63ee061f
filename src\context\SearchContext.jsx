import { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, getDocs, limit, where } from 'firebase/firestore';
import { db } from '../firebase/config';
import { getStorageItem, setStorageItem } from '../utils/localStorage';

// Constants
const SEARCH_DEBOUNCE = 300; // ms
const SEARCH_LIMIT = 10; // max results per category

const SearchContext = createContext();

export const useSearch = () => {
  return useContext(SearchContext);
};

export const SearchProvider = ({ children }) => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [recentSearches, setRecentSearches] = useState(() => {
    return getStorageItem('recentSearches', []);
  });

  // Toggle search panel
  const toggleSearch = () => {
    setSearchOpen(prevState => !prevState);
    if (searchOpen) {
      setSearchQuery('');
    }
  };

  // Save recent searches to localStorage
  useEffect(() => {
    setStorageItem('recentSearches', recentSearches);
  }, [recentSearches]);

  // Debounce search to avoid excessive API calls
  useEffect(() => {
    console.log("SearchContext effect triggered with query:", searchQuery);
    const delaySearch = setTimeout(() => {
      console.log("Performing search for:", searchQuery);
      performSearch(searchQuery);
    }, SEARCH_DEBOUNCE);

    return () => clearTimeout(delaySearch);
  }, [searchQuery]);

  // Helper function to search a Firestore collection
  const searchCollection = async (collectionName, query, searchFields, transformFn) => {
    try {
      // For small collections, get all and filter client-side
      // For large collections, consider using Firebase extensions like Algolia
      const collectionRef = collection(db, collectionName);
      const q = query(collectionRef, limit(20));
      const snapshot = await getDocs(q);
      
      // Client-side filtering
      return snapshot.docs
        .filter(doc => {
          const data = doc.data();
          return searchFields.some(field => {
            // Handle array fields (like tags)
            if (Array.isArray(data[field])) {
              return data[field].some(item => 
                item.toLowerCase().includes(query.toLowerCase())
              );
            }
            // Handle string fields
            return data[field] && 
                   typeof data[field] === 'string' && 
                   data[field].toLowerCase().includes(query.toLowerCase());
          });
        })
        .map(doc => transformFn({ id: doc.id, ...doc.data() }))
        .slice(0, SEARCH_LIMIT);
    } catch (error) {
      console.error(`Error searching ${collectionName}:`, error);
      return [];
    }
  };

  // Centralized search function that can be extended
  const performSearch = async (query) => {
    console.log("performSearch called with query:", query);
    if (!query.trim()) {
      console.log("Empty query, clearing results");
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    try {
      const lowerQuery = query.toLowerCase();
      let allResults = [];
      
      // Search products in Firestore
      const productsResults = await searchCollection(
        'products', 
        lowerQuery,
        ['name', 'description', 'category', 'tags'],
        (item) => ({
          id: item.id,
          type: 'product',
          displayName: item.name,
          path: `/shop/product/${item.id}`,
          image: item.imageUrl || 'https://placehold.co/100x100/png?text=Product',
          price: item.price
        })
      );
      
      // Search blog posts in Firestore
      const blogResults = await searchCollection(
        'blogPosts', 
        lowerQuery,
        ['title', 'excerpt', 'tags', 'content'],
        (item) => ({
          id: item.id,
          type: 'blog',
          displayName: item.title,
          path: `/blog/${item.id}`,
          date: item.publishDate
        })
      );
      
      // Search pages - static config
      const pagesConfig = [
        { id: 'home', title: 'Home', path: '/' },
        { id: 'shop', title: 'Shop', path: '/shop' },
        { id: 'contact', title: 'Contact Us', path: '/contact' },
        { id: 'faq', title: 'FAQ', path: '/faq' },
      ];
      
      const pagesResults = pagesConfig
        .filter(page => page.title.toLowerCase().includes(lowerQuery))
        .map(page => ({
          id: page.id,
          type: 'page',
          displayName: page.title,
          path: page.path
        }));
      
      // Combine all results
      allResults = [...productsResults, ...blogResults, ...pagesResults];
      
      // Sort by relevance
      allResults.sort((a, b) => {
        // Exact matches first
        const aExact = a.displayName.toLowerCase() === lowerQuery;
        const bExact = b.displayName.toLowerCase() === lowerQuery;
        
        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;
        
        // Then starts-with matches
        const aStarts = a.displayName.toLowerCase().startsWith(lowerQuery);
        const bStarts = b.displayName.toLowerCase().startsWith(lowerQuery);
        
        if (aStarts && !bStarts) return -1;
        if (!aStarts && bStarts) return 1;
        
        return 0;
      });
      
      console.log("Search results:", allResults);
      setSearchResults(allResults);
      
      // Add to recent searches if we got results
      if (allResults.length > 0 && query.trim()) {
        addToRecentSearches(query.trim());
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Add a search term to recent searches
  const addToRecentSearches = (term) => {
    setRecentSearches(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item !== term);
      // Add to beginning and limit to 5 items
      return [term, ...filtered].slice(0, 5);
    });
  };

  // Clear recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  // Handle search result click
  const handleResultClick = (result) => {
    // Add to recent searches
    if (result.displayName) {
      addToRecentSearches(result.displayName);
    }
    
    // Close search panel
    setSearchOpen(false);
    
    // Could add analytics tracking here
    console.log('Search result clicked:', result);
  };

  const value = {
    searchOpen,
    toggleSearch,
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching,
    recentSearches,
    clearRecentSearches,
    handleResultClick
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};









