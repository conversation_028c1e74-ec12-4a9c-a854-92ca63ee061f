import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { colors, spacing, typography } from '../styles';
import { useTheme } from '../context/ThemeContext';
import { FaCheckCircle } from 'react-icons/fa';

const SuccessContainer = styled.div`
  padding: ${spacing.xl};
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
`;

const SuccessIcon = styled.div`
  color: ${colors.success};
  font-size: 64px;
  margin-bottom: ${spacing.lg};
`;

const SuccessTitle = styled.h1`
  margin-bottom: ${spacing.md};
  font-size: ${typography.fontSize.xl};
`;

const SuccessMessage = styled.p`
  margin-bottom: ${spacing.xl};
  font-size: ${typography.fontSize.md};
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const OrderNumber = styled.div`
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lighter};
  padding: ${spacing.md};
  border-radius: 8px;
  margin-bottom: ${spacing.xl};
  display: inline-block;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: ${spacing.md};
  
  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const Button = styled(Link)`
  background-color: ${props => props.primary ? colors.primary.main : 'transparent'};
  color: ${props => props.primary ? 'white' : colors.primary.main};
  border: ${props => props.primary ? 'none' : `1px solid ${colors.primary.main}`};
  padding: ${spacing.md} ${spacing.lg};
  border-radius: 4px;
  text-decoration: none;
  font-weight: ${typography.fontWeight.medium};
  
  &:hover {
    background-color: ${props => props.primary ? colors.primary.dark : colors.primary.lighter};
  }
`;

const CheckoutSuccess = () => {
  const { darkMode } = useTheme();
  
  // Generate a random order number for demo purposes
  const orderNumber = `SG-${Math.floor(100000 + Math.random() * 900000)}`;
  
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);
  
  return (
    <SuccessContainer>
      <SuccessIcon>
        <FaCheckCircle />
      </SuccessIcon>
      
      <SuccessTitle>Order Confirmed!</SuccessTitle>
      
      <SuccessMessage darkMode={darkMode}>
        Thank you for your purchase. We've received your order and will begin processing it right away.
        You will receive a confirmation email shortly.
      </SuccessMessage>
      
      <OrderNumber darkMode={darkMode}>
        <strong>Order Number:</strong> {orderNumber}
      </OrderNumber>
      
      <ButtonContainer>
        <Button to="/shop" primary>Continue Shopping</Button>
        <Button to="/account">View My Orders</Button>
      </ButtonContainer>
    </SuccessContainer>
  );
};

export default CheckoutSuccess;
