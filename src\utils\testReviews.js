import { collection, getDocs, query, where, orderBy } from "firebase/firestore";
import { db } from "../firebase/config";
import { createTestReview } from "./createTestReview";

/**
 * Test function to check if reviews are being created and fetched correctly
 * @param {string} productId - The product ID to test with
 * @returns {Promise<Object>} - Test results
 */
export const testReviewSystem = async (productId) => {
  console.log("=== REVIEW SYSTEM TEST ===");
  console.log("Testing with product ID:", productId);
  
  try {
    // Step 1: Create a test review
    console.log("Step 1: Creating test review...");
    const reviewId = await createTestReview(productId);
    console.log("Test review created with ID:", reviewId);
    
    // Step 2: Fetch the review directly
    console.log("Step 2: Fetching review directly...");
    const reviewsRef = collection(db, "productReviews");
    const q = query(reviewsRef, where("productId", "==", productId));
    const snapshot = await getDocs(q);
    
    console.log("Query results:", {
      empty: snapshot.empty,
      size: snapshot.size,
      docs: snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    });
    
    // Step 3: Try with different query
    console.log("Step 3: Trying with different query...");
    const q2 = query(
      collection(db, "productReviews"),
      where("productId", "==", productId),
      orderBy("createdAt", "desc")
    );
    
    try {
      const snapshot2 = await getDocs(q2);
      console.log("Query with sort results:", {
        empty: snapshot2.empty,
        size: snapshot2.size
      });
    } catch (error) {
      console.error("Error with sorted query:", error.message);
      console.log("This is expected if you haven't created the index yet.");
    }
    
    return {
      success: true,
      reviewId,
      reviewCount: snapshot.size
    };
  } catch (error) {
    console.error("Test failed:", error);
    return {
      success: false,
      error: error.message
    };
  }
};
