import { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import Button from '../components/ui/Button';
import { colors, spacing, typography } from '../styles';

const ForgotPasswordContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
`;

const ForgotPasswordHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const ForgotPasswordTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const ForgotPasswordForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid ${props => props.$darkMode ? colors.neutral.gray700 : colors.neutral.gray300};
  border-radius: 4px;
  background-color: ${props => props.$darkMode ? colors.primary.gray800 : colors.neutral.white};
  color: ${props => props.$darkMode ? colors.neutral.gray800 : colors.neutral.gray900};
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light};
  }
`;

const ErrorMessage = styled.div`
  color: ${colors.error.main};
  background-color: ${props => props.$darkMode ? colors.error.dark : colors.error.light};
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.div`
  color: ${colors.success.main};
  background-color: ${props => props.$darkMode ? colors.success.dark : colors.success.light};
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const BackToLogin = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  
  a {
    color: ${colors.primary.main};
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const { resetPassword, error } = useAuth();
  const { darkMode } = useTheme();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    setLoading(true);
    
    try {
      const success = await resetPassword(email);
      if (success) {
        setMessage('Password reset email sent! Check your inbox for further instructions. Click the link in the email to reset your password.');
        setEmail(''); // Clear the email field
      }
    } catch (error) {
      console.error('Password reset error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ForgotPasswordContainer>
      <ForgotPasswordHeader>
        <ForgotPasswordTitle>Reset Password</ForgotPasswordTitle>
        <p>Enter your email address and we'll send you a link to reset your password.</p>
      </ForgotPasswordHeader>
      
      {error && <ErrorMessage $darkMode={darkMode}>{error}</ErrorMessage>}
      {message && <SuccessMessage $darkMode={darkMode}>{message}</SuccessMessage>}
      
      <ForgotPasswordForm onSubmit={handleSubmit}>
        <FormGroup>
          <label htmlFor="email">Email</label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            $darkMode={darkMode}
          />
        </FormGroup>
        
        <Button 
          type="submit" 
          disabled={loading}
          variant="primary"
          fullWidth
        >
          {loading ? 'Sending...' : 'Send Reset Link'}
        </Button>
      </ForgotPasswordForm>
      
      <BackToLogin>
        <Link to="/login">Back to Login</Link>
      </BackToLogin>
    </ForgotPasswordContainer>
  );
};

export default ForgotPassword;



