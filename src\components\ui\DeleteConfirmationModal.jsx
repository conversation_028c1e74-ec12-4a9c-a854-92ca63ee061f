import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography } from '../../styles';
import { FaExclamationTriangle } from 'react-icons/fa';

const DEEP_BLOOD_RED = '#8B0000'; // Deep blood red

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

const ModalContainer = styled(motion.div)`
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  padding: ${spacing.lg};
  width: 90%;
  max-width: 450px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const Title = styled.h3`
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  color: ${colors.error.main};
  font-family: ${typography.fontFamily.secondary};
`;

const Message = styled.p`
  margin-bottom: ${spacing.lg};
  line-height: 1.5;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${spacing.md};
`;

const Button = styled(motion.button)`
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 4px;
  border: none;
  font-weight: ${typography.fontWeight.medium};
  cursor: pointer;
  min-width: 100px;
`;

const CancelButton = styled(Button)`
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.light};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const DeleteButton = styled(Button)`
  background-color: ${DEEP_BLOOD_RED};
  color: white;
  
  &:hover {
    background-color: #6B0000; // Even darker on hover
  }
`;

// Update the DeleteConfirmationModal component to handle batch deletions
const DeleteConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  itemName, 
  darkMode 
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <Overlay
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <ModalContainer
            darkMode={darkMode}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            <Title>
              <FaExclamationTriangle /> Confirm Deletion
            </Title>
            <Message>
              Are you sure you want to delete "{itemName}"? This action cannot be undone.
            </Message>
            <ButtonGroup>
              <CancelButton
                darkMode={darkMode}
                onClick={onClose}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Cancel
              </CancelButton>
              <DeleteButton
                onClick={onConfirm}
                whileHover={{ scale: 1.05, backgroundColor: colors.error.dark }}
                whileTap={{ scale: 0.95 }}
              >
                Delete
              </DeleteButton>
            </ButtonGroup>
          </ModalContainer>
        </Overlay>
      )}
    </AnimatePresence>
  );
};

export default DeleteConfirmationModal;


