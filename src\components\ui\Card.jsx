import styled from 'styled-components';
import { colors, spacing } from '../../styles';
import { useTheme } from '../../context/ThemeContext';

const StyledCard = styled.div`
  background-color: ${props => props.darkMode ? colors.neutral.black : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  border-radius: 8px;
  border: ${props => props.darkMode ? `1px solid ${colors.neutral.dark}` : 'none'};
  box-shadow: ${props => props.darkMode ? 
    '0 2px 8px rgba(0, 0, 0, 0.5)' : 
    '0 2px 8px rgba(0, 0, 0, 0.08)'};
  padding: ${props => props.padding || spacing.lg};
  margin: ${props => props.margin || '0'};
  width: ${props => props.width || 'auto'};
  height: ${props => props.height || 'auto'};
  display: ${props => props.display || 'block'};
  flex-direction: ${props => props.flexDirection || 'column'};
  align-items: ${props => props.alignItems || 'stretch'};
  justify-content: ${props => props.justifyContent || 'flex-start'};
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  ${props => props.hoverable && `
    &:hover {
      transform: translateY(-4px);
      box-shadow: ${props.darkMode ? 
        '0 4px 12px rgba(0, 0, 0, 0.7)' : 
        '0 4px 12px rgba(0, 0, 0, 0.12)'};
    }
  `}
`;

const Card = (props) => {
  const { darkMode } = useTheme();
  return <StyledCard darkMode={darkMode} {...props} />;
};

export default Card;
