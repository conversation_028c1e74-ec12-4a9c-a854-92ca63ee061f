import styled from 'styled-components';
import { colors, spacing, typography } from '../../styles';

const DeleteButton = styled.button`
  background-color: ${colors.error.main || colors.error};
  color: white;
  border: none;
  border-radius: 4px;
  padding: ${spacing.xs} ${spacing.sm};
  font-size: 0.9rem;
  font-weight: ${typography.fontWeight.medium};
  cursor: pointer;
  width: 80px;
  max-width: 80px;
  margin-right: ${spacing.xs};
  transition: background-color 0.2s ease;
  
  &:hover:not(:disabled) {
    background-color: ${colors.error.dark || colors.errorDark || '#C62828'};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    max-width: 100%;
  }
`;

export default DeleteButton;
