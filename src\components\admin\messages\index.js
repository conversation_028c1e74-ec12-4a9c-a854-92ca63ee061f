// Export all message components for easy importing
import MessageList from './MessageList';
import MessageFilters from './MessageFilters';
import MessageActions, { BatchActions } from './MessageActions';
import MessageDetail from './MessageDetail';
import MessageExport from './MessageExport';
import * as MessageUtils from './MessageUtils';

// Export the main container component (when it's created)
// import MessageManagerContainer from './MessageManagerContainer';
// export default MessageManagerContainer;

// Export individual components
export {
  MessageList,
  MessageFilters,
  MessageActions,
  BatchActions,
  MessageDetail,
  MessageExport,
  MessageUtils
};
