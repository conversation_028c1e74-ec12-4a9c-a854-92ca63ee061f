import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '../../firebase/config';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { Link } from 'react-router-dom';

const SectionContainer = styled.section`
  padding: ${spacing['3xl']} ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Title = styled(motion.h2)`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.sm};
  font-family: ${typography.fontFamily.secondary};
  font-size: ${typography.fontSize['2xl']};
`;

const Subtitle = styled(motion.p)`
  color: ${props => props.darkMode ? colors.neutral.lighter : colors.neutral.dark};
  max-width: 600px;
  margin: 0 auto;
  font-size: ${typography.fontSize.md};
`;

const BlogsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: ${spacing.lg};
  margin-top: ${spacing.xl};
  
  @media (max-width: ${breakpoints.lg}) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: ${breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const BlogCard = styled(motion.div)`
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const BlogImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
`;

const BlogContent = styled.div`
  padding: ${spacing.md};
`;

const BlogTitle = styled.h3`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.xs};
  font-size: ${typography.fontSize.lg};
`;

const BlogExcerpt = styled.p`
  color: ${props => props.darkMode ? colors.neutral.lighter : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};
  margin-bottom: ${spacing.sm};
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const BlogMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${spacing.md};
  font-size: ${typography.fontSize.xs};
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const ViewAllLink = styled(motion.div)`
  text-align: center;
  margin-top: ${spacing.xl};
`;

const FeaturedBlogs = ({ darkMode }) => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFeaturedBlogs = async () => {
      try {
        // First try to get featured blogs
        let q = query(
          collection(db, 'blogPosts'),
          where('featured', '==', true),
          limit(3)
        );
        
        let querySnapshot = await getDocs(q);
        let featuredBlogs = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        // If no featured blogs or less than 3, get the most recent ones to fill
        if (featuredBlogs.length < 3) {
          const remainingCount = 3 - featuredBlogs.length;
          const featuredIds = featuredBlogs.map(blog => blog.id);
          
          // Use a simpler query without the composite index requirement
          q = query(
            collection(db, 'blogPosts'),
            orderBy('createdAt', 'desc'),
            limit(10) // Get more to ensure we have enough after filtering
          );
          
          querySnapshot = await getDocs(q);
          const recentBlogs = querySnapshot.docs
            .map(doc => ({
              id: doc.id,
              ...doc.data()
            }))
            .filter(blog => !featuredIds.includes(blog.id))
            .slice(0, remainingCount);
          
          featuredBlogs = [...featuredBlogs, ...recentBlogs];
        }
        
        setBlogs(featuredBlogs);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching featured blogs:', err);
        setError('Failed to load blog posts. Please try again later.');
        setLoading(false);
      }
    };

    fetchFeaturedBlogs();
  }, []);

  const formatDate = (date) => {
    if (!date) return '';
    
    // Handle Firestore Timestamp or Date object
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  };

  if (loading) {
    return (
      <SectionContainer darkMode={darkMode}>
        <SectionHeader>
          <Title darkMode={darkMode}>Skincare Journal</Title>
          <Subtitle darkMode={darkMode}>Loading our latest insights...</Subtitle>
        </SectionHeader>
      </SectionContainer>
    );
  }

  if (error) {
    return (
      <SectionContainer darkMode={darkMode}>
        <SectionHeader>
          <Title darkMode={darkMode}>Skincare Journal</Title>
          <Subtitle darkMode={darkMode}>{error}</Subtitle>
        </SectionHeader>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer darkMode={darkMode}>
      <SectionHeader>
        <Title 
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Skincare Journal
        </Title>
        <Subtitle 
          darkMode={darkMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Expert tips, beauty routines, and skincare wisdom from our specialists
        </Subtitle>
      </SectionHeader>
      
      {blogs.length > 0 ? (
        <>
          <BlogsGrid
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {blogs.map((blog, index) => (
              <BlogCard
                key={blog.id}
                darkMode={darkMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              >
                {blog.imageUrl && (
                  <BlogImage src={blog.imageUrl} alt={blog.title} />
                )}
                <BlogContent>
                  <BlogTitle darkMode={darkMode}>{blog.title}</BlogTitle>
                  <BlogExcerpt darkMode={darkMode}>
                    {blog.summary ? (
                      <div dangerouslySetInnerHTML={{ __html: blog.summary }} />
                    ) : (
                      <div dangerouslySetInnerHTML={{ __html: blog.content?.substring(0, 150) + '...' }} />
                    )}
                  </BlogExcerpt>
                  <BlogMeta darkMode={darkMode}>
                    <span>By {blog.author}</span>
                    <span>{formatDate(blog.publishDate || blog.createdAt)}</span>
                  </BlogMeta>
                </BlogContent>
              </BlogCard>
            ))}
          </BlogsGrid>
          
          <ViewAllLink
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Link 
              to="/blog" 
              style={{ 
                color: darkMode ? colors.primary.light : colors.primary.dark,
                fontSize: typography.fontSize.lg,
                fontWeight: 500,
                textDecoration: 'none',
                display: 'inline-block',
                padding: spacing.sm,
                borderBottom: `2px solid ${darkMode ? colors.primary.light : colors.primary.dark}`
              }}
            >
              View All Articles →
            </Link>
          </ViewAllLink>
        </>
      ) : (
        <Subtitle darkMode={darkMode} style={{ textAlign: 'center', marginTop: spacing.xl }}>
          No blog posts available at the moment. Check back soon!
        </Subtitle>
      )}
    </SectionContainer>
  );
};

export default FeaturedBlogs;




