import React, { useState } from 'react';
import styled from 'styled-components';
import { FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa';
import { colors } from '../../styles';

const RatingContainer = styled.div`
  display: flex;
  align-items: center;
`;

const StarContainer = styled.div`
  display: inline-flex;
  cursor: ${props => props.interactive ? 'pointer' : 'default'};
  color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
`;

const RatingText = styled.span`
  margin-left: 8px;
  font-size: 0.9rem;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

/**
 * Star Rating Component
 * @param {Object} props
 * @param {number} props.rating - Current rating value (0-5)
 * @param {boolean} props.interactive - Whether the stars can be clicked to set rating
 * @param {function} props.onChange - Callback when rating changes (only used if interactive)
 * @param {boolean} props.showValue - Whether to show the numeric rating value
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {number} props.size - Size of the stars in pixels
 * @param {number} props.count - Number of reviews (optional)
 * @returns {JSX.Element}
 */
const StarRating = ({ 
  rating = 0, 
  interactive = false, 
  onChange = () => {}, 
  showValue = false,
  darkMode = false,
  size = 20,
  count = null
}) => {
  const [hoverRating, setHoverRating] = useState(0);
  
  // Convert rating to array of stars
  const getStars = (value) => {
    const stars = [];
    const fullStars = Math.floor(value);
    const hasHalfStar = value - fullStars >= 0.5;
    
    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={`star-${i}`} size={size} />);
    }
    
    // Add half star if needed
    if (hasHalfStar) {
      stars.push(<FaStarHalfAlt key="half-star" size={size} />);
    }
    
    // Add empty stars
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FaRegStar key={`empty-star-${i}`} size={size} />);
    }
    
    return stars;
  };
  
  // Handle mouse enter on star
  const handleMouseEnter = (index) => {
    if (interactive) {
      setHoverRating(index);
    }
  };
  
  // Handle mouse leave on container
  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };
  
  // Handle click on star
  const handleClick = (index) => {
    if (interactive) {
      onChange(index);
    }
  };
  
  // Get display rating (hover rating or actual rating)
  const displayRating = hoverRating || rating;
  
  // Get stars based on display rating
  const stars = getStars(displayRating);
  
  return (
    <RatingContainer onMouseLeave={handleMouseLeave}>
      <StarContainer darkMode={darkMode} interactive={interactive}>
        {stars.map((star, index) => (
          <span 
            key={index}
            onMouseEnter={() => handleMouseEnter(index + 1)}
            onClick={() => handleClick(index + 1)}
          >
            {star}
          </span>
        ))}
      </StarContainer>
      
      {showValue && (
        <RatingText darkMode={darkMode}>
          {displayRating.toFixed(1)}
          {count !== null && ` (${count})`}
        </RatingText>
      )}
    </RatingContainer>
  );
};

export default StarRating;
