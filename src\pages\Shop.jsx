import { useState, useEffect, useRef } from "react";
import { useSearchParams, Link } from "react-router-dom";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../styles";
import { useTheme } from "../context/ThemeContext";
import { collection, getDocs, query, where } from "firebase/firestore";
import { db } from "../firebase/config";
import { useCart } from "../context/CartContext";
import ProductCard from "../components/products/ProductCard";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";

// Import category icons
import allproducts from "../assets/images/shop/all-products.jpeg";
import faceIcon from "../assets/images/shop/face-care.jpeg";
import lipsIcon from "../assets/images/shop/lips.jpeg";
import bodyIcon from "../assets/images/shop/body-care.jpeg";
import hairIcon from "../assets/images/shop/hair-care.jpeg";
import giftSetIcon from "../assets/images/shop/gift-set.jpeg";

const ShopContainer = styled.div`
  padding: ${spacing.xl};
`;

const ShopHeader = styled.div`
  margin-bottom: ${spacing.xl};
`;

const ShopTitle = styled.h1`
  margin-bottom: ${spacing.md};
`;

const ScrollableWrapper = styled.div`
  position: relative;
  width: 100%;
  margin-bottom: ${spacing.xl};

  @media (max-width: ${breakpoints.sm}) {
    overflow: hidden;
    padding: 0;
    margin-left: -${spacing.lg}; /* Extend beyond the container */
    margin-right: -${spacing.lg}; /* Extend beyond the container */
    width: calc(100% + ${spacing.xl}); /* Make it wider */
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: ${spacing.md};
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;

  @media (max-width: ${breakpoints.sm}) {
    flex-wrap: nowrap;
    justify-content: flex-start;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    padding: ${spacing.xs} ${spacing.xl} ${spacing.sm}; /* Add horizontal padding */

    /* Center items when there's not enough to fill the width */
    &::after {
      content: "";
      padding-right: ${spacing.xl};
    }

    /* Hide scrollbar but keep functionality */
    &::-webkit-scrollbar {
      height: 0;
      width: 0;
      display: none;
    }
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
`;

const FilterButton = styled(Link)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-5px);
  }

  @media (max-width: ${breakpoints.sm}) {
    flex: 0 0 auto;
    padding: ${spacing.sm};
    width: 110px; /* Slightly wider for better visibility */
  }
`;

const IconImage = styled.img`
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid
    ${(props) => (props.active ? colors.primary.main : "transparent")};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${colors.primary.light};
  }
`;

const IconLabel = styled.span`
  margin-top: ${spacing.xs};
  font-size: ${typography.fontSize.sm};
  color: ${(props) =>
    props.active ? colors.primary.main : colors.neutral.dark};
  font-weight: ${(props) =>
    props.active ? typography.fontWeight.bold : typography.fontWeight.regular};
`;

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: ${spacing.lg};

  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const ButtonsWrapper = styled.div`
  display: flex;
  gap: ${spacing.sm};
  margin-top: ${spacing.md};
`;

const ViewDetailsButton = styled(Link)`
  background-color: ${colors.secondary.main};
  color: white;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  text-decoration: none;
  text-align: center;
  flex: 1;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${colors.secondary.dark};
  }
`;

const AddToCartButton = styled.button`
  background-color: ${colors.primary.main};
  color: white;
  border: none;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  cursor: pointer;
  flex: 1;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${colors.primary.dark};
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: ${spacing.xl};
  gap: ${spacing.sm};
  flex-wrap: wrap;
`;

const PageButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 ${spacing.sm};
  border-radius: 4px;
  background-color: ${(props) =>
    props.active
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) => (props.active ? colors.primary.main : colors.neutral.gray)};
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};
  transition: all 0.2s ease;
  font-weight: ${(props) =>
    props.active ? typography.fontWeight.bold : typography.fontWeight.regular};

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.active
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.darker
        : colors.neutral.light};
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
`;

const Shop = () => {
  const [searchParams] = useSearchParams();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const { darkMode } = useTheme();
  const { addToCart } = useCart();
  const filtersRef = useRef(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  const category = searchParams.get("category") || "all";

  // Add a resize listener to track window width
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Determine products per page based on screen size
  const getProductsPerPage = () => {
    if (windowWidth < parseInt(breakpoints.sm)) {
      return 4; // 4 products per page on mobile
    } else if (windowWidth < parseInt(breakpoints.md)) {
      return 6; // 6 products per page on tablets
    } else {
      return 8; // 8 products per page on desktop
    }
  };

  const productsPerPage = getProductsPerPage();

  // Reset to page 1 when category changes or screen size affects productsPerPage
  useEffect(() => {
    setCurrentPage(1);
  }, [category, productsPerPage]);

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        let productsQuery;

        if (category && category !== "all") {
          productsQuery = query(
            collection(db, "products"),
            where("category", "==", category)
          );
        } else {
          productsQuery = collection(db, "products");
        }

        const snapshot = await getDocs(productsQuery);
        const productsList = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));

        setProducts(productsList);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [category]);

  const filteredProducts =
    category === "all"
      ? products
      : products.filter((product) => product.category === category);

  // Pagination logic
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top when changing page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Determine how many page buttons to show based on screen size
  const getVisiblePageRange = () => {
    // For mobile screens
    if (windowWidth < parseInt(breakpoints.sm)) {
      // Show only current page, prev and next buttons
      return 0;
    }
    // For tablet screens
    else if (windowWidth < parseInt(breakpoints.md)) {
      // Show current page and one on each side
      return 1;
    }
    // For desktop screens
    else {
      // Show current page and two on each side
      return 2;
    }
  };

  const handleAddToCart = (product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      imageUrl: product.imageUrl,
    });
  };

  return (
    <ShopContainer>
      <ShopHeader>
        <ShopTitle>Shop Our Products</ShopTitle>
        <p>
          Discover our handcrafted skincare products made with premium natural
          ingredients.
        </p>
      </ShopHeader>

      <ScrollableWrapper>
        <FiltersContainer ref={filtersRef}>
          <FilterButton to="/shop" active={category === "all"}>
            <IconImage
              src={`${allproducts}#${Date.now()}`}
              alt="All Products"
              active={category === "all"}
              style={{
                border: `3px solid ${
                  category === "all" ? colors.primary.main : "transparent"
                }`,
                filter: "brightness(1.1)",
              }}
            />
            <IconLabel active={category === "all"}>All Products</IconLabel>
          </FilterButton>
          <FilterButton to="/shop?category=face" active={category === "face"}>
            <IconImage
              src={faceIcon}
              alt="Face Care"
              active={category === "face"}
            />
            <IconLabel active={category === "face"}>Face Care</IconLabel>
          </FilterButton>
          <FilterButton to="/shop?category=lips" active={category === "lips"}>
            <IconImage
              src={lipsIcon}
              alt="Lip Care"
              active={category === "lips"}
            />
            <IconLabel active={category === "lips"}>Lip Care</IconLabel>
          </FilterButton>
          <FilterButton to="/shop?category=body" active={category === "body"}>
            <IconImage
              src={bodyIcon}
              alt="Body Care"
              active={category === "body"}
            />
            <IconLabel active={category === "body"}>Body Care</IconLabel>
          </FilterButton>
          <FilterButton to="/shop?category=hair" active={category === "hair"}>
            <IconImage
              src={hairIcon}
              alt="Hair Care"
              active={category === "hair"}
            />
            <IconLabel active={category === "hair"}>Hair Care</IconLabel>
          </FilterButton>
          <FilterButton to="/shop?category=sets" active={category === "sets"}>
            <IconImage
              src={giftSetIcon}
              alt="Gift Sets"
              active={category === "sets"}
            />
            <IconLabel active={category === "sets"}>Gift Sets</IconLabel>
          </FilterButton>
        </FiltersContainer>
      </ScrollableWrapper>

      {loading ? (
        <p>Loading products...</p>
      ) : (
        <>
          <ProductsGrid>
            {currentProducts.map((product) => (
              <div key={product.id} style={{ position: "relative" }}>
                <ProductCard product={product} />
                {/* <ButtonsWrapper>
                  <ViewDetailsButton to={`/shop/product/${product.id}`}>
                    View Details
                  </ViewDetailsButton>
                  <AddToCartButton onClick={() => handleAddToCart(product)}>
                    Add to Cart
                  </AddToCartButton>
                </ButtonsWrapper> */}
              </div>
            ))}
          </ProductsGrid>

          {/* Pagination */}
          {filteredProducts.length > productsPerPage && (
            <PaginationContainer>
              <PageButton
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                darkMode={darkMode}
              >
                <FaChevronLeft />
              </PageButton>

              {[...Array(totalPages)].map((_, index) => {
                const pageNumber = index + 1;
                const visibleRange = getVisiblePageRange();

                // Always show first and last page
                const showFirstLast = pageNumber === 1 || pageNumber === totalPages;

                // Show pages within the visible range of current page
                const showInRange = pageNumber >= currentPage - visibleRange &&
                                   pageNumber <= currentPage + visibleRange;

                // Determine if we should show this page number
                if (showFirstLast || showInRange) {
                  return (
                    <PageButton
                      key={pageNumber}
                      active={currentPage === pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      darkMode={darkMode}
                    >
                      {pageNumber}
                    </PageButton>
                  );
                }

                // Show ellipsis for skipped pages, but only if there's a gap
                if (
                  (pageNumber === 2 && currentPage > visibleRange + 2) ||
                  (pageNumber === totalPages - 1 && currentPage < totalPages - visibleRange - 1)
                ) {
                  return <span key={pageNumber} style={{ alignSelf: 'center' }}>...</span>;
                }

                return null;
              })}

              <PageButton
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                darkMode={darkMode}
              >
                <FaChevronRight />
              </PageButton>
            </PaginationContainer>
          )}

          {/* Show message when no products are found */}
          {filteredProducts.length === 0 && (
            <div style={{ textAlign: 'center', marginTop: spacing.xl }}>
              <h3>No products found in this category</h3>
              <p>Try selecting a different category or check back later for new products.</p>
            </div>
          )}
        </>
      )}
    </ShopContainer>
  );
};

export default Shop;
