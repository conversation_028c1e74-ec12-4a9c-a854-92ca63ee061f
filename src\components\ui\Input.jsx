import styled from 'styled-components';
import { colors, typography, spacing } from '../../styles';

const InputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: ${spacing.md};
  width: ${props => props.fullWidth ? '100%' : 'auto'};
`;

const Label = styled.label`
  font-family: ${typography.fontFamily.primary};
  font-size: ${typography.fontSize.sm};
  font-weight: ${typography.fontWeight.medium};
  margin-bottom: ${spacing.xs};
  color: ${colors.neutral.dark};
`;

const StyledInput = styled.input`
  font-family: ${typography.fontFamily.primary};
  font-size: ${typography.fontSize.base};
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid ${props => props.error ? colors.feedback.error : colors.neutral.medium};
  border-radius: 4px;
  outline: none;
  transition: border-color 0.2s ease;
  
  &:focus {
    border-color: ${props => props.error ? colors.feedback.error : colors.primary.main};
    box-shadow: 0 0 0 2px ${props => props.error ? 
      `${colors.feedback.error}20` : `${colors.primary.main}20`};
  }
  
  &::placeholder {
    color: ${colors.neutral.gray};
  }
  
  &:disabled {
    background-color: ${colors.neutral.light};
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.span`
  font-family: ${typography.fontFamily.primary};
  font-size: ${typography.fontSize.xs};
  color: ${colors.feedback.error};
  margin-top: ${spacing.xs};
`;

const Input = ({ 
  label, 
  error, 
  fullWidth = false, 
  ...props 
}) => {
  return (
    <InputWrapper fullWidth={fullWidth}>
      {label && <Label>{label}</Label>}
      <StyledInput error={error} {...props} />
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </InputWrapper>
  );
};

export default Input;