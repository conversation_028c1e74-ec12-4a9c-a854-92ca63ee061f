import { collection, getDocs } from 'firebase/firestore';
import { db } from './config';
import searchClient from '../utils/algoliaClient';

// Function to verify Algolia sync
export const verifyAlgoliaSync = async () => {
  try {
    // Check Firestore products
    console.log("Checking Firestore products...");
    const productsSnapshot = await getDocs(collection(db, 'products'));
    const firestoreProductCount = productsSnapshot.size;
    console.log(`Firestore products count: ${firestoreProductCount}`);
    
    // Check Algolia products index
    console.log("Checking Algolia products index...");
    const productsIndex = searchClient.initIndex('products');
    const algoliaStats = await productsIndex.search('', { hitsPerPage: 0 });
    console.log(`Algolia products index count: ${algoliaStats.nbHits}`);
    
    // Check if counts match
    if (firestoreProductCount !== algoliaStats.nbHits) {
      console.warn("Warning: Firestore and Algolia product counts don't match!");
    } else {
      console.log("Success: Firestore and Algolia product counts match!");
    }
    
    return {
      firestoreCount: firestoreProductCount,
      algoliaCount: algoliaStats.nbHits,
      synced: firestoreProductCount === algoliaStats.nbHits
    };
  } catch (error) {
    console.error("Error verifying Algolia sync:", error);
    return { error: error.message };
  }
};

