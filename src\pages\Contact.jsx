import React from 'react';
import styled from 'styled-components';
import { colors, spacing } from '../styles';
import { useTheme } from '../context/ThemeContext';
import ContactForm from '../components/contact/ContactForm';
import { motion } from 'framer-motion';
import { FaEnvelope, FaMapMarkerAlt, FaPhone } from 'react-icons/fa';

const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${spacing.xl} ${spacing.md};
`;

const ContactInfo = styled(motion.div)`
  margin-bottom: ${spacing.xl};
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${spacing.xl};
  
  @media (min-width: 768px) {
    grid-template-columns: 2fr 1fr;
  }
`;

const ContactCards = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.lg};
`;

const ContactCard = styled(motion.div)`
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.lightest};
  padding: ${spacing.lg};
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const IconContainer = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${colors.primary.light};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
`;

const CardContent = styled.div`
  h3 {
    margin: 0 0 ${spacing.xs} 0;
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  }
  
  p {
    margin: 0;
    color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  }
`;

const Contact = () => {
  const { darkMode } = useTheme();
  
  return (
    <PageContainer>
      <ContactInfo 
        darkMode={darkMode}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.h1
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Get in Touch
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          We'd love to hear from you! Fill out the form below and we'll get back to you as soon as possible.
        </motion.p>
      </ContactInfo>
      
      <ContactGrid>
        <ContactForm />
        
        <ContactCards>
          <ContactCard 
            darkMode={darkMode}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            whileHover={{ scale: 1.03 }}
          >
            <IconContainer>
              <FaMapMarkerAlt />
            </IconContainer>
            <CardContent darkMode={darkMode}>
              <h3>Our Location</h3>
              <p>123 Botanical Street, Green City, 10001</p>
            </CardContent>
          </ContactCard>
          
          <ContactCard 
            darkMode={darkMode}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            whileHover={{ scale: 1.03 }}
          >
            <IconContainer>
              <FaPhone />
            </IconContainer>
            <CardContent darkMode={darkMode}>
              <h3>Phone Number</h3>
              <p>(*************</p>
            </CardContent>
          </ContactCard>
          
          <ContactCard 
            darkMode={darkMode}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            whileHover={{ scale: 1.03 }}
          >
            <IconContainer>
              <FaEnvelope />
            </IconContainer>
            <CardContent darkMode={darkMode}>
              <h3>Email Address</h3>
              <p><EMAIL></p>
            </CardContent>
          </ContactCard>
        </ContactCards>
      </ContactGrid>
    </PageContainer>
  );
};

export default Contact;

