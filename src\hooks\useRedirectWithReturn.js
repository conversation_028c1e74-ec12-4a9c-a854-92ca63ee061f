import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

/**
 * A custom hook that saves the current location for redirecting back after login
 * 
 * @param {boolean} shouldSave - Whether to save the current location
 * @returns {Object} - The savePreviousLocation function from AuthContext
 */
const useRedirectWithReturn = (shouldSave = true) => {
  const location = useLocation();
  const { savePreviousLocation } = useAuth();
  
  useEffect(() => {
    if (shouldSave) {
      savePreviousLocation(location.pathname);
    }
  }, [location, savePreviousLocation, shouldSave]);
  
  return { savePreviousLocation };
};

export default useRedirectWithReturn;
