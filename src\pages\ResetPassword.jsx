import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { auth } from '../firebase/config';
import { verifyPasswordResetCode, confirmPasswordReset } from 'firebase/auth';
import { useTheme } from '../context/ThemeContext';
import Button from '../components/ui/Button';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { colors, spacing, typography } from '../styles';

const ResetPasswordContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
`;

const ResetPasswordHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const ResetPasswordTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const ResetPasswordForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  padding-right: 2.5rem; /* Add space for the eye icon */
  border: 1px solid ${props => props.$darkMode ? colors.neutral.gray700 : colors.neutral.gray300};
  border-radius: 4px;
  background-color: ${props => props.$darkMode ? colors.neutral.gray800 : colors.neutral.white};
  color: ${props => props.$darkMode ? colors.neutral.gray800 : colors.neutral.gray900};
  
  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
    box-shadow: 0 0 0 2px ${colors.primary.light};
  }
`;

const PasswordInputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const PasswordToggleButton = styled.button`
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.$darkMode ? colors.neutral.gray400 : colors.neutral.gray600};
  z-index: 1; /* Ensure button is clickable */
  padding: 0.25rem;
  
  &:hover {
    color: ${colors.primary.main};
  }
`;

const ErrorMessage = styled.div`
  color: ${colors.error.main};
  background-color: ${props => props.$darkMode ? colors.error.dark : colors.error.light};
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.div`
  color: ${colors.success.main};
  background-color: ${props => props.$darkMode ? colors.success.dark : colors.success.light};
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const ResetPassword = () => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(true);
  const [actionCode, setActionCode] = useState('');
  const [validCode, setValidCode] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const navigate = useNavigate();
  const location = useLocation();
  const { darkMode } = useTheme();
  
  useEffect(() => {
    // Get the action code from the URL
    const queryParams = new URLSearchParams(location.search);
    const code = queryParams.get('oobCode');
    
    if (!code) {
      setError('Invalid password reset link. Please request a new one.');
      setLoading(false);
      return;
    }
    
    setActionCode(code);
    
    // Verify the action code
    verifyPasswordResetCode(auth, code)
      .then((email) => {
        setEmail(email);
        setValidCode(true);
        setLoading(false);
      })
      .catch((error) => {
        console.error('Error verifying reset code:', error);
        setError('This password reset link has expired or is invalid. Please request a new one.');
        setLoading(false);
      });
  }, [location]);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    if (newPassword.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Confirm password reset with Firebase
      await confirmPasswordReset(auth, actionCode, newPassword);
      
      setMessage('Your password has been reset successfully! Redirecting to login...');
      
      // Redirect to login page after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      console.error('Error resetting password:', error);
      
      switch(error.code) {
        case 'auth/weak-password':
          setError('Password is too weak. Please choose a stronger password.');
          break;
        case 'auth/expired-action-code':
          setError('This password reset link has expired. Please request a new one.');
          break;
        case 'auth/invalid-action-code':
          setError('This password reset link is invalid. Please request a new one.');
          break;
        default:
          setError('Failed to reset password. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };
  
  if (loading) {
    return (
      <ResetPasswordContainer>
        <ResetPasswordHeader>
          <ResetPasswordTitle>Reset Password</ResetPasswordTitle>
          <p>Verifying your reset link...</p>
        </ResetPasswordHeader>
      </ResetPasswordContainer>
    );
  }
  
  if (!validCode) {
    return (
      <ResetPasswordContainer>
        <ResetPasswordHeader>
          <ResetPasswordTitle>Reset Password</ResetPasswordTitle>
        </ResetPasswordHeader>
        
        <ErrorMessage $darkMode={darkMode}>{error}</ErrorMessage>
        
        <Button 
          onClick={() => navigate('/forgot-password')}
          variant="primary"
          fullWidth
        >
          Request New Reset Link
        </Button>
      </ResetPasswordContainer>
    );
  }
  
  return (
    <ResetPasswordContainer>
      <ResetPasswordHeader>
        <ResetPasswordTitle>Reset Password</ResetPasswordTitle>
        <p>Enter a new password for {email}</p>
      </ResetPasswordHeader>
      
      {error && <ErrorMessage $darkMode={darkMode}>{error}</ErrorMessage>}
      {message && <SuccessMessage $darkMode={darkMode}>{message}</SuccessMessage>}
      
      <ResetPasswordForm onSubmit={handleSubmit}>
        <FormGroup>
          <label htmlFor="newPassword">New Password</label>
          <PasswordInputWrapper>
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              $darkMode={darkMode}
            />
            <PasswordToggleButton 
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              $darkMode={darkMode}
            >
              {showNewPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggleButton>
          </PasswordInputWrapper>
        </FormGroup>
        
        <FormGroup>
          <label htmlFor="confirmPassword">Confirm Password</label>
          <PasswordInputWrapper>
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              $darkMode={darkMode}
            />
            <PasswordToggleButton 
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              $darkMode={darkMode}
            >
              {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggleButton>
          </PasswordInputWrapper>
        </FormGroup>
        
        <Button 
          type="submit" 
          disabled={loading || !!message}
          variant="primary"
          fullWidth
        >
          {loading ? 'Resetting...' : 'Reset Password'}
        </Button>
      </ResetPasswordForm>
    </ResetPasswordContainer>
  );
};

export default ResetPassword;

