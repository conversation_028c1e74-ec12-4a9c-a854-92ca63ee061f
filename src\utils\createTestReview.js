import { collection, addDoc, serverTimestamp } from "firebase/firestore";
import { db, auth } from "../firebase/config";

/**
 * Creates a test review for a product
 * @param {string} productId - The ID of the product to review
 * @returns {Promise<string>} - The ID of the created review
 */
export const createTestReview = async (productId) => {
  try {
    // Check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      throw new Error("You must be logged in to create a review");
    }

    // Create test review data
    const reviewData = {
      productId,
      userId: user.uid,
      userName: user.displayName || "Test User",
      rating: 4,
      title: "Test Review",
      comment:
        "This is a test review to verify the review system functionality. The product is great!",
      verified: true, // For testing purposes
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      helpful: 0,
      notHelpful: 0,
      reported: false,
      status: "approved", // Admin-created test reviews are auto-approved
    };

    // Add the review to Firestore
    const docRef = await addDoc(collection(db, "productReviews"), reviewData);

    console.log("Test review created with ID:", docRef.id);
    return docRef.id;
  } catch (error) {
    console.error("Error creating test review:", error);
    throw error;
  }
};

/**
 * Creates multiple test reviews for a product
 * @param {string} productId - The ID of the product to review
 * @param {number} count - Number of reviews to create
 * @returns {Promise<string[]>} - Array of created review IDs
 */
export const createMultipleTestReviews = async (productId, count = 5) => {
  try {
    // Check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      throw new Error("You must be logged in to create reviews");
    }

    const reviewIds = [];

    // Sample review data
    const reviewTitles = [
      "Great product!",
      "Exceeded my expectations",
      "Good value for money",
      "Would recommend to friends",
      "Will buy again",
    ];

    const reviewComments = [
      "I love this product! It has made a significant difference to my skin.",
      "The quality is excellent. I noticed results within a week of using it.",
      "This product is worth every penny. My skin feels so much better.",
      "I was skeptical at first, but now I'm a believer. This stuff works!",
      "The scent is amazing and the results are even better. Highly recommend!",
    ];

    // Create multiple reviews
    for (let i = 0; i < count; i++) {
      const rating = Math.floor(Math.random() * 3) + 3; // Random rating between 3-5
      const titleIndex = i % reviewTitles.length;
      const commentIndex = i % reviewComments.length;

      // Create review with slightly different data
      const reviewData = {
        productId,
        userId: user.uid,
        userName: user.displayName || `Test User ${i + 1}`,
        rating,
        title: reviewTitles[titleIndex],
        comment: reviewComments[commentIndex],
        verified: i % 2 === 0, // Alternate between verified and unverified
        createdAt: serverTimestamp(), // Use Firestore server timestamp
        updatedAt: serverTimestamp(),
        helpful: Math.floor(Math.random() * 5),
        notHelpful: Math.floor(Math.random() * 2),
        reported: false,
        // Create a mix of statuses for testing the moderation queue
        status: i % 3 === 0 ? "pending" : i % 3 === 1 ? "approved" : "rejected",
      };

      // Add the review to Firestore
      const docRef = await addDoc(collection(db, "productReviews"), reviewData);
      reviewIds.push(docRef.id);
    }

    console.log(`Created ${count} test reviews`);
    return reviewIds;
  } catch (error) {
    console.error("Error creating test reviews:", error);
    throw error;
  }
};
