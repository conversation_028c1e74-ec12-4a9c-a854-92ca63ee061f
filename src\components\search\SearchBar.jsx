import { useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSearch, FaTimes } from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { useAlgoliaSearch } from '../../context/AlgoliaSearchContext';
import { colors } from '../../styles';
import {
  SearchContainer,
  SearchInputWrapper,
  SearchInput,
  SearchButton,
  ResultsContainer,
  ResultItem,
  ResultTitle,
  ResultType,
  NoResults
} from './SearchBar.styles';

const SearchBar = () => {
  const { 
    searchQuery, 
    setSearchQuery, 
    searchResults, 
    isSearching,
    searchOpen
  } = useAlgoliaSearch();
  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const inputRef = useRef(null);
  
  useEffect(() => {
    if (searchOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [searchOpen]);
  
  const handleInputChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  const handleClearSearch = () => {
    setSearchQuery('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  
  const handleResultClick = (result) => {
    navigate(result.path);
    setSearchQuery('');
  };
  
  const renderResultsByType = () => {
    console.log("Rendering results:", searchResults);
    
    if (searchResults.length === 0) {
      if (isSearching) {
        return <NoResults darkMode={darkMode}>Searching...</NoResults>;
      }
      if (searchQuery.trim()) {
        return <NoResults darkMode={darkMode}>No results found</NoResults>;
      }
      return null;
    }
    
    return searchResults.map(result => (
      <ResultItem 
        key={`${result.type}-${result.id}`} 
        onClick={() => handleResultClick(result)}
        darkMode={darkMode}
      >
        <ResultTitle darkMode={darkMode}>{result.displayName}</ResultTitle>
        <ResultType darkMode={darkMode}>{result.type}</ResultType>
      </ResultItem>
    ));
  };
  
  return (
    <SearchContainer>
      <SearchInputWrapper darkMode={darkMode}>
        <FaSearch color={darkMode ? colors.neutral.gray : colors.primary.main} size={16} />
        <SearchInput
          ref={inputRef}
          type="text"
          placeholder="Search products, blog posts, pages..."
          value={searchQuery}
          onChange={handleInputChange}
          darkMode={darkMode}
        />
        {searchQuery && (
          <SearchButton onClick={handleClearSearch} darkMode={darkMode}>
            <FaTimes size={16} />
          </SearchButton>
        )}
      </SearchInputWrapper>
      
      {searchQuery && (
        <ResultsContainer darkMode={darkMode}>
          {console.log("Rendering ResultsContainer with query:", searchQuery)}
          {renderResultsByType()}
        </ResultsContainer>
      )}
    </SearchContainer>
  );
};

export default SearchBar;







