import { createContext, useState, useContext, useEffect } from 'react';
import GlobalStyles from '../styles/GlobalStyles';
import { getStorageItem, setStorageItem } from '../utils/localStorage';

const ThemeContext = createContext();

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  const [darkMode, setDarkMode] = useState(() => {
    return getStorageItem('darkMode', false);
  });

  useEffect(() => {
    // Apply theme class to body
    document.body.classList.toggle('dark-mode', darkMode);
    
    // Save preference to localStorage
    setStorageItem('darkMode', darkMode);
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };

  const value = {
    darkMode,
    toggleDarkMode
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
      <GlobalStyles darkMode={darkMode} />
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
