import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { colors, spacing, typography } from '../styles';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { createTestReview, createMultipleTestReviews } from '../utils/createTestReview';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${spacing.xl};
`;

const Title = styled.h1`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.lg};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const Section = styled.div`
  margin-bottom: ${spacing.xl};
  padding: ${spacing.lg};
  background-color: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lightest};
  border-radius: 8px;
`;

const SectionTitle = styled.h2`
  font-family: ${typography.fontFamily.secondary};
  margin-bottom: ${spacing.md};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
`;

const Button = styled.button`
  background-color: ${colors.primary.main};
  color: white;
  border: none;
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 4px;
  cursor: pointer;
  margin-right: ${spacing.md};
  margin-bottom: ${spacing.md};
  
  &:hover {
    background-color: ${colors.primary.dark};
  }
  
  &:disabled {
    background-color: ${colors.neutral.gray};
    cursor: not-allowed;
  }
`;

const Input = styled.input`
  padding: ${spacing.sm};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  margin-right: ${spacing.md};
  margin-bottom: ${spacing.md};
  width: 300px;
`;

const Message = styled.div`
  padding: ${spacing.md};
  margin: ${spacing.md} 0;
  border-radius: 4px;
  background-color: ${props => props.type === 'success' 
    ? colors.success 
    : props.type === 'error' 
      ? colors.error 
      : colors.info};
  color: white;
`;

const ProductList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${spacing.md};
  margin-top: ${spacing.lg};
`;

const ProductCard = styled.div`
  padding: ${spacing.md};
  border: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.light};
  border-radius: 4px;
  background-color: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  
  h3 {
    margin-bottom: ${spacing.sm};
    color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  }
  
  p {
    color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
    margin-bottom: ${spacing.xs};
  }
`;

const ReviewTest = () => {
  const { darkMode } = useTheme();
  const { user, isAuthenticated } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [selectedProductId, setSelectedProductId] = useState('');
  const [reviewCount, setReviewCount] = useState(5);
  
  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const productsRef = collection(db, 'products');
        const q = query(productsRef, orderBy('name'));
        const snapshot = await getDocs(q);
        const productsList = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setProducts(productsList);
      } catch (error) {
        console.error('Error fetching products:', error);
        setMessage({ text: `Error fetching products: ${error.message}`, type: 'error' });
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, []);
  
  // Handle creating a test review
  const handleCreateTestReview = async () => {
    if (!selectedProductId) {
      setMessage({ text: 'Please select a product first', type: 'error' });
      return;
    }
    
    try {
      setMessage({ text: 'Creating test review...', type: 'info' });
      const reviewId = await createTestReview(selectedProductId);
      setMessage({ text: `Test review created with ID: ${reviewId}`, type: 'success' });
    } catch (error) {
      console.error('Error creating test review:', error);
      setMessage({ text: `Error creating test review: ${error.message}`, type: 'error' });
    }
  };
  
  // Handle creating multiple test reviews
  const handleCreateMultipleReviews = async () => {
    if (!selectedProductId) {
      setMessage({ text: 'Please select a product first', type: 'error' });
      return;
    }
    
    try {
      setMessage({ text: `Creating ${reviewCount} test reviews...`, type: 'info' });
      const reviewIds = await createMultipleTestReviews(selectedProductId, parseInt(reviewCount));
      setMessage({ text: `Created ${reviewIds.length} test reviews`, type: 'success' });
    } catch (error) {
      console.error('Error creating test reviews:', error);
      setMessage({ text: `Error creating test reviews: ${error.message}`, type: 'error' });
    }
  };
  
  return (
    <Container>
      <Title darkMode={darkMode}>Review System Test Page</Title>
      
      {!isAuthenticated && (
        <Message type="error">
          You must be logged in to create test reviews. Please log in first.
        </Message>
      )}
      
      {message.text && (
        <Message type={message.type}>{message.text}</Message>
      )}
      
      <Section darkMode={darkMode}>
        <SectionTitle darkMode={darkMode}>Create Test Reviews</SectionTitle>
        
        <div>
          <label htmlFor="product-select">Select a product: </label>
          <Input
            type="text"
            id="product-select"
            list="products-list"
            placeholder="Type or select a product"
            value={selectedProductId}
            onChange={(e) => setSelectedProductId(e.target.value)}
            darkMode={darkMode}
          />
          <datalist id="products-list">
            {products.map(product => (
              <option key={product.id} value={product.id}>
                {product.name}
              </option>
            ))}
          </datalist>
        </div>
        
        <div style={{ marginTop: spacing.md }}>
          <Button 
            onClick={handleCreateTestReview}
            disabled={!isAuthenticated || !selectedProductId}
          >
            Create Single Test Review
          </Button>
          
          <Input
            type="number"
            min="1"
            max="20"
            value={reviewCount}
            onChange={(e) => setReviewCount(e.target.value)}
            style={{ width: '80px' }}
            darkMode={darkMode}
          />
          
          <Button 
            onClick={handleCreateMultipleReviews}
            disabled={!isAuthenticated || !selectedProductId}
          >
            Create Multiple Test Reviews
          </Button>
        </div>
      </Section>
      
      <Section darkMode={darkMode}>
        <SectionTitle darkMode={darkMode}>Available Products</SectionTitle>
        
        {loading ? (
          <p>Loading products...</p>
        ) : (
          <ProductList>
            {products.map(product => (
              <ProductCard key={product.id} darkMode={darkMode} onClick={() => setSelectedProductId(product.id)}>
                <h3>{product.name}</h3>
                <p>ID: {product.id}</p>
                <p>Category: {product.category}</p>
                <p>Price: ${product.price.toFixed(2)}</p>
              </ProductCard>
            ))}
          </ProductList>
        )}
      </Section>
    </Container>
  );
};

export default ReviewTest;
