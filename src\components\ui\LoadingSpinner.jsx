import React from 'react';
import styled, { keyframes } from 'styled-components';
import { colors } from '../../styles';
import { useTheme } from '../../context/ThemeContext';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
`;

const Spinner = styled.div`
  border: 4px solid ${props => props.$darkMode ? colors.neutral.gray700 : colors.neutral.gray300};
  border-top: 4px solid ${colors.primary.main};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: ${spin} 1s linear infinite;
`;

const LoadingSpinner = () => {
  const { darkMode } = useTheme();
  
  return (
    <SpinnerContainer>
      <Spinner $darkMode={darkMode} />
    </SpinnerContainer>
  );
};

export default LoadingSpinner;