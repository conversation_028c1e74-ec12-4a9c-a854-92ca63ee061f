const sgMail = require('@sendgrid/mail');
const admin = require('firebase-admin');

// Initialize SendGrid with API key from environment
const initializeSendGrid = () => {
  const apiKey = process.env.SENDGRID_API_KEY;
  if (!apiKey) {
    console.error('SENDGRID_API_KEY environment variable is not set');
    return false;
  }
  sgMail.setApiKey(apiKey);
  return true;
};

// Email configuration
const EMAIL_CONFIG = {
  fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
  fromName: process.env.FROM_NAME || 'SkinGlow',
  replyTo: process.env.REPLY_TO_EMAIL || '<EMAIL>',
  companyName: 'SkinGlow',
  companyWebsite: 'https://skinglow.com',
  supportEmail: '<EMAIL>'
};

// Base email template with beautiful styling
const getBaseTemplate = (content, title = '') => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background-color: #f8f9fa;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 12px; 
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            font-size: 28px; 
            font-weight: 300; 
            margin-bottom: 5px; 
        }
        .header p { 
            opacity: 0.9; 
            font-size: 16px; 
        }
        .content { 
            padding: 40px 30px; 
        }
        .footer { 
            background: #f8f9fa; 
            padding: 30px; 
            text-align: center; 
            border-top: 1px solid #e9ecef; 
        }
        .footer p { 
            color: #6c757d; 
            font-size: 14px; 
            margin-bottom: 10px; 
        }
        .button { 
            display: inline-block; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 12px 30px; 
            text-decoration: none; 
            border-radius: 25px; 
            font-weight: 500; 
            margin: 20px 0; 
        }
        .order-details { 
            background: #f8f9fa; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .order-item { 
            display: flex; 
            justify-content: space-between; 
            padding: 10px 0; 
            border-bottom: 1px solid #e9ecef; 
        }
        .order-item:last-child { 
            border-bottom: none; 
            font-weight: 600; 
        }
        .highlight { 
            color: #667eea; 
            font-weight: 600; 
        }
        @media (max-width: 600px) {
            .container { margin: 10px; }
            .content { padding: 30px 20px; }
            .header { padding: 25px 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${EMAIL_CONFIG.companyName}</h1>
            <p>Premium Skincare & Beauty</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>Thank you for choosing ${EMAIL_CONFIG.companyName}</p>
            <p>
                <a href="${EMAIL_CONFIG.companyWebsite}" style="color: #667eea;">Visit our website</a> | 
                <a href="mailto:${EMAIL_CONFIG.supportEmail}" style="color: #667eea;">Contact Support</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                This email was sent from ${EMAIL_CONFIG.companyName}. 
                If you have any questions, please contact us at ${EMAIL_CONFIG.supportEmail}
            </p>
        </div>
    </div>
</body>
</html>`;
};

// Order confirmation email template
const getOrderConfirmationTemplate = (orderData) => {
  const { orderId, customerName, items, total, shippingAddress, paymentMethod } = orderData;
  
  const itemsHtml = items.map(item => `
    <div class="order-item">
      <span>${item.name} ${item.variant ? `(${item.variant})` : ''} x ${item.quantity}</span>
      <span>$${(item.price * item.quantity).toFixed(2)}</span>
    </div>
  `).join('');

  const content = `
    <h2>Order Confirmation</h2>
    <p>Hi ${customerName},</p>
    <p>Thank you for your order! We're excited to get your ${EMAIL_CONFIG.companyName} products to you.</p>
    
    <div class="order-details">
      <h3 style="margin-bottom: 15px; color: #333;">Order #${orderId}</h3>
      ${itemsHtml}
      <div class="order-item">
        <span><strong>Total</strong></span>
        <span><strong>$${total.toFixed(2)}</strong></span>
      </div>
    </div>

    <h3 style="margin-top: 30px; margin-bottom: 15px;">Shipping Address</h3>
    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
      ${shippingAddress.line1}<br>
      ${shippingAddress.line2 ? `${shippingAddress.line2}<br>` : ''}
      ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.postal_code}<br>
      ${shippingAddress.country}
    </p>

    <h3 style="margin-bottom: 15px;">Payment Method</h3>
    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
      ${paymentMethod}
    </p>

    <p>We'll send you another email when your order ships. If you have any questions, please don't hesitate to contact us.</p>
    
    <a href="${EMAIL_CONFIG.companyWebsite}/account" class="button">View Order Status</a>
  `;

  return getBaseTemplate(content, `Order Confirmation - ${orderId}`);
};

// Contact form notification templates
const getContactFormCustomerTemplate = (formData) => {
  const { name, subject } = formData;
  
  const content = `
    <h2>Thank You for Contacting Us!</h2>
    <p>Hi ${name},</p>
    <p>We've received your message about "<span class="highlight">${subject}</span>" and will get back to you within 24 hours.</p>
    
    <p>Our customer service team is dedicated to providing you with the best possible experience. We appreciate your patience and look forward to assisting you.</p>
    
    <p>If you have any urgent concerns, please don't hesitate to call us directly.</p>
    
    <a href="${EMAIL_CONFIG.companyWebsite}" class="button">Visit Our Website</a>
  `;

  return getBaseTemplate(content, 'Thank You for Your Message');
};

const getContactFormAdminTemplate = (formData) => {
  const { name, email, subject, message, date } = formData;
  
  const content = `
    <h2>New Contact Form Submission</h2>
    <p>You have received a new message from your website contact form.</p>
    
    <div class="order-details">
      <div class="order-item">
        <span><strong>Name:</strong></span>
        <span>${name}</span>
      </div>
      <div class="order-item">
        <span><strong>Email:</strong></span>
        <span>${email}</span>
      </div>
      <div class="order-item">
        <span><strong>Subject:</strong></span>
        <span>${subject}</span>
      </div>
      <div class="order-item">
        <span><strong>Date:</strong></span>
        <span>${new Date(date).toLocaleString()}</span>
      </div>
    </div>

    <h3 style="margin-top: 30px; margin-bottom: 15px;">Message:</h3>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; white-space: pre-wrap;">${message}</div>
    
    <a href="${EMAIL_CONFIG.companyWebsite}/admin" class="button">View in Admin Panel</a>
  `;

  return getBaseTemplate(content, `New Contact: ${subject}`);
};

// Send email function
const sendEmail = async (to, subject, htmlContent, options = {}) => {
  if (!initializeSendGrid()) {
    throw new Error('SendGrid not properly configured');
  }

  const msg = {
    to,
    from: {
      email: EMAIL_CONFIG.fromEmail,
      name: EMAIL_CONFIG.fromName
    },
    replyTo: options.replyTo || EMAIL_CONFIG.replyTo,
    subject,
    html: htmlContent,
    ...options
  };

  try {
    const response = await sgMail.send(msg);
    console.log('Email sent successfully:', { to, subject });
    
    // Log email to Firestore for tracking
    await logEmailToFirestore({
      to,
      subject,
      status: 'sent',
      sentAt: new Date(),
      messageId: response[0].headers['x-message-id']
    });
    
    return response;
  } catch (error) {
    console.error('Error sending email:', error);
    
    // Log failed email to Firestore
    await logEmailToFirestore({
      to,
      subject,
      status: 'failed',
      error: error.message,
      sentAt: new Date()
    });
    
    throw error;
  }
};

// Log email to Firestore for tracking
const logEmailToFirestore = async (emailData) => {
  try {
    const db = admin.firestore();
    await db.collection('email_logs').add(emailData);
  } catch (error) {
    console.error('Error logging email to Firestore:', error);
  }
};

module.exports = {
  sendEmail,
  getOrderConfirmationTemplate,
  getContactFormCustomerTemplate,
  getContactFormAdminTemplate,
  EMAIL_CONFIG
};
