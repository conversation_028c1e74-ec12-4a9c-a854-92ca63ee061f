const functions = require("firebase-functions");

// Email configuration
const EMAIL_CONFIG = {
  fromEmail: functions.config().email?.from_email || "<EMAIL>",
  fromName: functions.config().email?.from_name || "SkinGlow",
  replyTo: functions.config().email?.reply_to || "<EMAIL>",
  companyName: "SkinGlow",
  companyWebsite: "https://skinglow.com",
  supportEmail:
    functions.config().email?.support_email || "<EMAIL>",
};

// Initialize SendGrid only when needed
const initializeSendGrid = () => {
  try {
    // Check if SendGrid package is available
    let sgMail;
    try {
      sgMail = require("@sendgrid/mail");
    } catch (requireError) {
      console.log("SendGrid package not available:", requireError.message);
      return null;
    }

    const apiKey = functions.config().sendgrid?.api_key;

    if (!apiKey) {
      console.log("SendGrid API key not found in Firebase config");
      return null;
    }

    sgMail.setApiKey(apiKey);
    console.log("SendGrid initialized successfully");
    return sgMail;
  } catch (error) {
    console.error("Error initializing SendGrid:", error);
    return null;
  }
};

// Base email template with beautiful styling
const getBaseTemplate = (content, title = "") => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background-color: #f8f9fa;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 12px; 
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            font-size: 28px; 
            font-weight: 300; 
            margin-bottom: 5px; 
        }
        .header p { 
            opacity: 0.9; 
            font-size: 16px; 
        }
        .content { 
            padding: 40px 30px; 
        }
        .footer { 
            background: #f8f9fa; 
            padding: 30px; 
            text-align: center; 
            border-top: 1px solid #e9ecef; 
        }
        .footer p { 
            color: #6c757d; 
            font-size: 14px; 
            margin-bottom: 10px; 
        }
        .button { 
            display: inline-block; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 12px 30px; 
            text-decoration: none; 
            border-radius: 25px; 
            font-weight: 500; 
            margin: 20px 0; 
        }
        .order-details { 
            background: #f8f9fa; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .order-item { 
            display: flex; 
            justify-content: space-between; 
            padding: 10px 0; 
            border-bottom: 1px solid #e9ecef; 
        }
        .order-item:last-child { 
            border-bottom: none; 
            font-weight: 600; 
        }
        .highlight { 
            color: #667eea; 
            font-weight: 600; 
        }
        @media (max-width: 600px) {
            .container { margin: 10px; }
            .content { padding: 30px 20px; }
            .header { padding: 25px 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${EMAIL_CONFIG.companyName}</h1>
            <p>Premium Skincare & Beauty</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>Thank you for choosing ${EMAIL_CONFIG.companyName}</p>
            <p>
                <a href="${EMAIL_CONFIG.companyWebsite}" style="color: #667eea;">Visit our website</a> | 
                <a href="mailto:${EMAIL_CONFIG.supportEmail}" style="color: #667eea;">Contact Support</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                This email was sent from ${EMAIL_CONFIG.companyName}. 
                If you have any questions, please contact us at ${EMAIL_CONFIG.supportEmail}
            </p>
        </div>
    </div>
</body>
</html>`;
};

// Send email function with fallback
const sendEmail = async (to, subject, htmlContent, options = {}) => {
  const admin = require("firebase-admin");

  try {
    const sgMail = initializeSendGrid();

    if (!sgMail) {
      // Fallback: Log to Firestore if SendGrid is not available
      console.log("SendGrid not available, logging email to Firestore");
      await admin
        .firestore()
        .collection("email_logs")
        .add({
          to,
          subject,
          htmlContent: htmlContent.substring(0, 1000) + "...", // Truncate for storage
          status: "logged_only",
          sentAt: new Date(),
          reason: "sendgrid_not_configured",
        });
      return { success: true, method: "logged" };
    }

    const msg = {
      to,
      from: {
        email: EMAIL_CONFIG.fromEmail,
        name: EMAIL_CONFIG.fromName,
      },
      replyTo: options.replyTo || EMAIL_CONFIG.replyTo,
      subject,
      html: htmlContent,
      ...options,
    };

    const response = await sgMail.send(msg);
    console.log("Email sent successfully:", { to, subject });

    // Log successful email to Firestore
    await admin.firestore().collection("email_logs").add({
      to,
      subject,
      status: "sent",
      sentAt: new Date(),
      messageId: response[0].headers["x-message-id"],
    });

    return { success: true, method: "sendgrid", response };
  } catch (error) {
    console.error("Error sending email:", error);

    // Log failed email to Firestore
    await admin.firestore().collection("email_logs").add({
      to,
      subject,
      status: "failed",
      error: error.message,
      sentAt: new Date(),
    });

    throw error;
  }
};

// Order confirmation email template
const getOrderConfirmationTemplate = (orderData) => {
  const {
    orderId,
    customerName,
    items,
    total,
    shippingAddress,
    paymentMethod,
  } = orderData;

  const itemsHtml = items
    .map(
      (item) => `
    <div class="order-item">
      <span>${item.name} ${item.variant ? `(${item.variant})` : ""} x ${
        item.quantity
      }</span>
      <span>$${(item.price * item.quantity).toFixed(2)}</span>
    </div>
  `
    )
    .join("");

  const content = `
    <h2>Order Confirmation</h2>
    <p>Hi ${customerName},</p>
    <p>Thank you for your order! We're excited to get your ${
      EMAIL_CONFIG.companyName
    } products to you.</p>
    
    <div class="order-details">
      <h3 style="margin-bottom: 15px; color: #333;">Order #${orderId}</h3>
      ${itemsHtml}
      <div class="order-item">
        <span><strong>Total</strong></span>
        <span><strong>$${total.toFixed(2)}</strong></span>
      </div>
    </div>

    <h3 style="margin-top: 30px; margin-bottom: 15px;">Shipping Address</h3>
    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
      ${shippingAddress.line1}<br>
      ${shippingAddress.line2 ? `${shippingAddress.line2}<br>` : ""}
      ${shippingAddress.city}, ${shippingAddress.state} ${
    shippingAddress.postal_code
  }<br>
      ${shippingAddress.country}
    </p>

    <h3 style="margin-bottom: 15px;">Payment Method</h3>
    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
      ${paymentMethod}
    </p>

    <p>We'll send you another email when your order ships. If you have any questions, please don't hesitate to contact us.</p>
    
    <a href="${
      EMAIL_CONFIG.companyWebsite
    }/account" class="button">View Order Status</a>
  `;

  return getBaseTemplate(content, `Order Confirmation - ${orderId}`);
};

module.exports = {
  sendEmail,
  getBaseTemplate,
  getOrderConfirmationTemplate,
  EMAIL_CONFIG,
};
