import { useState, useEffect } from 'react';
import { verifyAlgoliaSync } from '../firebase/verifyAlgoliaSync';

const AlgoliaSyncCheck = () => {
  const [syncStatus, setSyncStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const checkSync = async () => {
      try {
        const status = await verifyAlgoliaSync();
        setSyncStatus(status);
      } catch (error) {
        console.error("Error checking sync:", error);
      } finally {
        setLoading(false);
      }
    };
    
    checkSync();
  }, []);
  
  if (loading) return <div>Checking Algolia sync status...</div>;
  
  if (syncStatus?.error) {
    return <div>Error checking sync: {syncStatus.error}</div>;
  }
  
  return (
    <div>
      <h3>Algolia Sync Status</h3>
      <p>Firestore Products: {syncStatus?.firestoreCount || 0}</p>
      <p>Algolia Products: {syncStatus?.algoliaCount || 0}</p>
      <p>Status: {syncStatus?.synced ? '✅ Synced' : '❌ Not Synced'}</p>
    </div>
  );
};

export default AlgoliaSyncCheck;