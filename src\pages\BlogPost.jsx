import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import styled from 'styled-components';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useTheme } from '../context/ThemeContext';

const BlogPostContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

const BlogPostHeader = styled.div`
  margin-bottom: 2rem;
`;

const BlogPostTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const BlogPostImage = styled.img`
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 2rem;
`;

const BlogPostContent = styled.div`
  line-height: 1.8;
`;

const BackLink = styled(Link)`
  display: inline-block;
  margin-top: 2rem;
  color: #333;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
`;

const BlogPostMeta = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  color: ${props => props.darkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'};
  font-size: 0.9rem;
`;

const BlogPost = () => {
  const { id } = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const { darkMode } = useTheme();
  
  const formatDate = (date) => {
    if (!date) return '';
    
    // Handle Firestore Timestamp or Date object
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  };

  useEffect(() => {
    const fetchBlogPost = async () => {
      setLoading(true);
      try {
        const docRef = doc(db, 'blogPosts', id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          setPost({
            id: docSnap.id,
            ...docSnap.data()
          });
        } else {
          console.log("No such document!");
        }
      } catch (error) {
        console.error('Error fetching blog post:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBlogPost();
  }, [id]);
  
  if (loading) {
    return (
      <BlogPostContainer>
        <p>Loading blog post...</p>
      </BlogPostContainer>
    );
  }
  
  if (!post) {
    return (
      <BlogPostContainer>
        <p>Blog post not found</p>
        <BackLink to="/blog">Back to Blog</BackLink>
      </BlogPostContainer>
    );
  }
  
  return (
    <BlogPostContainer>
      <BlogPostHeader>
        <BlogPostTitle>{post.title}</BlogPostTitle>
        <BlogPostMeta darkMode={darkMode}>
          {post.author && <span>By {post.author}</span>}
          {(post.createdAt || post.publishDate) && 
            <span>{formatDate(post.createdAt || post.publishDate)}</span>}
        </BlogPostMeta>
      </BlogPostHeader>
      
      {post.imageUrl && <BlogPostImage src={post.imageUrl} alt={post.title} />}
      
      <BlogPostContent dangerouslySetInnerHTML={{ __html: post.content }} />
      
      <BackLink to="/blog">← Back to Blog</BackLink>
    </BlogPostContainer>
  );
};

export default BlogPost;


