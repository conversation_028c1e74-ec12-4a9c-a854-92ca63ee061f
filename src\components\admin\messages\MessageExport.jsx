import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FaFileCsv, FaFilePdf } from 'react-icons/fa';
import jsPDF from 'jspdf';
import 'jspdf-autotable'; // This import is critical - it adds the autoTable method to jsPDF
import { formatReadTimestamp, formatMessageContent } from './MessageUtils';

const colors = {
  success: {
    light: '#e8f5e9',
    main: '#4caf50',
    dark: '#2e7d32'
  },
  warning: {
    light: '#fff3e0',
    main: '#ff9800',
    dark: '#f57c00'
  },
  neutral: {
    white: '#ffffff'
  }
};

const ExportContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
`;

const ExportButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  background: ${props => props.darkMode 
    ? props.type === 'csv' 
      ? colors.success?.dark || '#2e7d32' 
      : colors.warning?.dark || '#f57c00'
    : props.type === 'csv'
      ? colors.success?.light || '#e8f5e9'
      : colors.warning?.light || '#fff3e0'
  };
  color: ${props => props.darkMode 
    ? colors.neutral.white 
    : props.type === 'csv'
      ? colors.success?.dark || '#2e7d32'
      : colors.warning?.dark || '#f57c00'
  };
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.darkMode 
      ? props.type === 'csv' 
        ? colors.success?.main || '#4caf50' 
        : colors.warning?.main || '#ff9800'
      : props.type === 'csv'
        ? colors.success?.main || '#4caf50'
        : colors.warning?.main || '#ff9800'
    };
    color: ${props => colors.neutral.white};
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: none;
  }
`;

const MessageExport = ({ filteredMessages, darkMode, formatMessageContent }) => {
  const exportToCSV = () => {
    const dataToExport = filteredMessages;
    
    if (dataToExport.length === 0) {
      alert('No messages to export');
      return;
    }
    
    try {
      // Create a more structured and professional CSV
      const rows = [];
      
      // Add metadata headers for better organization
      rows.push(['# Message Export Report']);
      rows.push([`# Generated on: ${new Date().toLocaleDateString('en-US', { 
        year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' 
      })}`]);
      rows.push([`# Total messages: ${dataToExport.length}`]);
      rows.push([]);  // Empty row as separator
      
      // Add column headers with better descriptions
      rows.push([
        "Subject", 
        "Sender Name", 
        "Sender Email", 
        "Date Received", 
        "Status", 
        "Read Timestamp", 
        "Priority", 
        "Category", 
        "Message Content"
      ]);
      
      // Add data rows with improved formatting
      dataToExport.forEach((message, index) => {
        const subject = message.subject || 'No Subject';
        const from = message.name || 'Unknown';
        const email = message.email || '';
        
        // Format date more professionally
        const date = message.date 
          ? new Date(message.date).toLocaleDateString('en-US', { 
              year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' 
            }) 
          : '';
        
        const status = message.isRead ? 'Read' : 'Unread';
        const readAt = message.readAt ? formatReadTimestamp(message.readAt) : 'N/A';
        
        // Add some additional useful fields (if they exist in your data model)
        const priority = message.priority || 'Normal';
        const category = message.category || 'General';
        
        // Clean and format message content
        let messageContent = message.message || '';
        if (typeof formatMessageContent === 'function') {
          messageContent = formatMessageContent(messageContent);
        }

        // Instead of replacing newlines with spaces, properly escape them for CSV
        // This will preserve the original formatting when opened in a spreadsheet
        messageContent = messageContent.replace(/"/g, '""'); // Double escape quotes for CSV
        
        rows.push([
          subject, 
          from, 
          email, 
          date, 
          status, 
          readAt, 
          priority, 
          category, 
          messageContent
        ]);
      });
      
      // Convert rows to CSV content with proper escaping
      let csvContent = rows.map(row => 
        row.map(cell => {
          // Handle null/undefined values
          if (cell === null || cell === undefined) {
            return '';
          }
          
          const cellStr = String(cell);
          // Properly escape cells with quotes, commas, or newlines
          if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
            return `"${cellStr.replace(/"/g, '""')}"`;
          }
          return cellStr;
        }).join(',')
      ).join('\n');
      
      // Add UTF-8 BOM for better Excel compatibility
      const BOM = '\uFEFF';
      csvContent = BOM + csvContent;
      
      // Create Blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      
      // Add timestamp to filename for better organization
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      link.setAttribute("download", `messages_export_${timestamp}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      alert('Error exporting to CSV. Please check console for details.');
    }
  };

  const exportToPDF = () => {
    const dataToExport = filteredMessages;
    
    if (dataToExport.length === 0) {
      alert('No messages to export');
      return;
    }
    
    try {
      // Create new jsPDF document
      const doc = new jsPDF();
      
      // Add company logo/header with a professional touch
      doc.setFillColor(245, 245, 245);
      doc.rect(0, 0, 210, 30, 'F');
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.5);
      doc.line(0, 30, 210, 30);
      
      doc.setFontSize(22);
      doc.setTextColor(40, 40, 40);
      doc.setFont("helvetica", "bold");
      doc.text('Message Export Report', 14, 20);
      
      // Add export date with better formatting
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.setFont("helvetica", "normal");
      const exportDate = new Date().toLocaleDateString('en-US', { 
        year: 'numeric', month: 'long', day: 'numeric' 
      });
      doc.text(`Generated on: ${exportDate}`, 14, 40);
      
      // Add message count
      doc.text(`Total messages: ${dataToExport.length}`, 14, 46);
      
      // Add a separator line
      doc.setDrawColor(80, 80, 80);
      doc.setLineWidth(0.5);
      doc.line(14, 50, 196, 50);
      
      // Add summary table on first page
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.setTextColor(40, 40, 40);
      doc.text('Message Summary', 14, 60);
      
      // Create summary table with all messages
      const tableData = dataToExport.map(message => [
        message.subject || 'No Subject',
        message.name || 'Unknown',
        message.date ? new Date(message.date).toLocaleDateString() : 'No date',
        message.isRead ? 'Read' : 'Unread'
      ]);
      
      // Add the summary table using basic jsPDF methods instead of autoTable
      const headers = ['Subject', 'From', 'Date', 'Status'];
      
      // Draw table headers
      doc.setFillColor(60, 60, 60);
      doc.setTextColor(255, 255, 255);
      doc.setFont("helvetica", "bold");
      doc.setFontSize(9);
      
      let startY = 65;
      let cellWidth = [50, 40, 30, 20];
      let tableWidth = cellWidth.reduce((a, b) => a + b, 0);
      let startX = (210 - tableWidth) / 2;
      
      // Draw header cells
      headers.forEach((header, i) => {
        doc.setFillColor(60, 60, 60);
        doc.rect(startX, startY, cellWidth[i], 10, 'F');
        doc.text(header, startX + cellWidth[i]/2, startY + 6, { align: 'center' });
        startX += cellWidth[i];
      });
      
      // Draw data rows
      doc.setTextColor(0, 0, 0);
      doc.setFont("helvetica", "normal");
      
      tableData.forEach((row, rowIndex) => {
        startY += 10;
        startX = (210 - tableWidth) / 2;
        
        // Alternate row background
        if (rowIndex % 2 === 0) {
          doc.setFillColor(245, 245, 245);
          doc.rect(startX, startY, tableWidth, 10, 'F');
        }
        
        row.forEach((cell, cellIndex) => {
          // Truncate text if too long
          let text = cell.toString();
          if (text.length > 20) {
            text = text.substring(0, 17) + '...';
          }
          
          doc.text(text, startX + cellWidth[cellIndex]/2, startY + 6, { align: 'center' });
          startX += cellWidth[cellIndex];
        });
        
        // Check if we need a new page
        if (startY > 250) {
          doc.addPage();
          startY = 20;
        }
      });
      
      // Add detailed message pages after the summary
      doc.addPage();
      
      // Add header to each page
      doc.setFillColor(245, 245, 245);
      doc.rect(0, 0, 210, 20, 'F');
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.5);
      doc.line(0, 20, 210, 20);
      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      doc.setTextColor(60, 60, 60);
      doc.text('Message Details', 14, 15);
      
      let y = 30; // Starting y position for content
      
      // Loop through messages and add them to the PDF with better formatting
      dataToExport.forEach((message, index) => {
        // Check if we need a new page
        if (y > 260) {
          doc.addPage();
          // Add header to new page
          doc.setFillColor(245, 245, 245);
          doc.rect(0, 0, 210, 20, 'F');
          doc.setDrawColor(200, 200, 200);
          doc.setLineWidth(0.5);
          doc.line(0, 20, 210, 20);
          doc.setFontSize(12);
          doc.setFont("helvetica", "bold");
          doc.setTextColor(60, 60, 60);
          doc.text('Message Details', 14, 15);
          y = 30;
        }
        
        // Message card with shadow effect
        doc.setFillColor(252, 252, 252);
        doc.rect(14, y, 182, 80, 'F');
        
        // Message header with better styling
        doc.setFillColor(240, 240, 240);
        doc.rect(14, y, 182, 10, 'F');
        doc.setFontSize(11);
        doc.setFont("helvetica", "bold");
        doc.setTextColor(60, 60, 60);
        doc.text(`Message ${index + 1}`, 16, y + 7);
        
        // Add timestamp on the right
        if (message.date) {
          const dateStr = new Date(message.date).toLocaleDateString();
          doc.setFontSize(9);
          doc.setFont("helvetica", "normal");
          doc.text(dateStr, 180, y + 7, { align: 'right' });
        }
        y += 15;
        
        // Subject with better styling
        doc.setFontSize(12);
        doc.setFont("helvetica", "bold");
        doc.setTextColor(40, 40, 40);
        doc.text(message.subject || 'No Subject', 18, y);
        y += 8;
        
        // From and email with better styling
        doc.setFontSize(9);
        doc.setFont("helvetica", "normal");
        doc.setTextColor(80, 80, 80);
        const fromText = `From: ${message.name || 'Unknown'}`;
        const emailText = message.email ? `Email: ${message.email}` : '';
        doc.text(fromText, 18, y);
        if (emailText) {
          doc.text(emailText, 120, y);
        }
        y += 6;
        
        // Status with colored indicators
        const statusText = `Status: ${message.isRead ? 'Read' : 'Unread'}`;
        doc.text(statusText, 18, y);
        
        // Add colored status indicator
        if (message.isRead) {
          doc.setFillColor(76, 175, 80); // Green for read
        } else {
          doc.setFillColor(255, 152, 0); // Orange for unread
        }
        doc.circle(45, y - 2, 2, 'F');
        
        y += 6;
        
        // Read timestamp if available
        if (message.isRead && message.readAt) {
          doc.text(`Read at: ${formatReadTimestamp(message.readAt)}`, 18, y);
          y += 6;
        }
        
        // Message content with better formatting
        doc.setFontSize(9);
        doc.setTextColor(0, 0, 0);
        if (message.message) {
          // Add a light background for the message content
          doc.setFillColor(248, 248, 248);
          doc.rect(18, y, 174, 40, 'F');
          
          // Format message content if the function is available
          let messageText = message.message;
          if (typeof formatMessageContent === 'function') {
            messageText = formatMessageContent(message.message);
          }
          
          const splitText = doc.splitTextToSize(messageText, 165);
          doc.text(splitText, 22, y + 5); // Add some padding inside the box
          y += Math.min(splitText.length * 5 + 10, 45); // Limit height and add padding
        } else {
          doc.setFillColor(248, 248, 248);
          doc.rect(18, y, 174, 15, 'F');
          doc.text('No message content', 22, y + 8);
          y += 20;
        }
        
        y += 15; // Add more space between messages
      });
      
      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`Page ${i} of ${pageCount}`, 196, 285, { align: 'right' });
      }
      
      doc.save(`messages_export_${new Date().toISOString().slice(0,10)}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please check console for details.');
    }
  };

  return (
    <ExportContainer>
      <ExportButton 
        darkMode={darkMode}
        onClick={exportToCSV}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        type="csv"
      >
        <FaFileCsv /> Export to CSV
      </ExportButton>
      <ExportButton 
        darkMode={darkMode}
        onClick={exportToPDF}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        type="pdf"
      >
        <FaFilePdf /> Export to PDF
      </ExportButton>
    </ExportContainer>
  );
};

export default MessageExport;
