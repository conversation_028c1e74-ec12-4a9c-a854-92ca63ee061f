import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";
import { FaGoogle, FaEye, FaEyeSlash } from "react-icons/fa";
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import { auth } from "../firebase/config";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import Button from "../components/ui/Button";
import { colors } from "../styles";

const RegisterContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
`;

const RegisterHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const RegisterTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const RegisterForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  padding-right: 2.5rem; /* Make room for the eye icon */
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
`;

const ErrorMessage = styled.p`
  color: red;
  font-size: 0.875rem;
`;

const LoginLink = styled.p`
  text-align: center;
  margin-top: 1.5rem;
`;

const GoogleButton = styled(Button)`
  background-color: ${(props) =>
    props.$darkMode ? colors.primary.dark : colors.primary.main};
  color: ${colors.neutral.white};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 1rem;

  &:hover {
    background-color: ${(props) =>
      props.$darkMode ? colors.primary.main : colors.primary.light};
  }
`;

const PasswordInputWrapper = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
`;

const PasswordToggleButton = styled.button`
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: ${(props) =>
    props.$darkMode ? colors.neutral.gray400 : colors.neutral.gray600};
  z-index: 1; /* Ensure button is clickable */
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${colors.primary.main};
  }
`;

const OrDivider = styled.div`
  display: flex;
  align-items: center;
  margin: 1rem 0;

  &:before,
  &:after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #ddd;
  }

  span {
    padding: 0 10px;
    color: #777;
    font-size: 0.9rem;
  }
`;

const Register = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { signInWithGoogle, getSavedLocation, clearSavedLocation } = useAuth();
  const { darkMode } = useTheme();

  // Check for redirect parameter in URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const redirectPath = searchParams.get("redirect");

    if (redirectPath) {
      // Convert the redirect parameter to a full path
      const fullRedirectPath = `/${redirectPath}`;
      localStorage.setItem("authRedirectPath", fullRedirectPath);
    }
  }, [location]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    setLoading(true);

    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      await updateProfile(userCredential.user, { displayName: name });

      // Get the saved redirect path or default to account page
      const redirectPath = getSavedLocation();
      // Clear the saved path to avoid unexpected redirects in the future
      clearSavedLocation();
      // Navigate to the redirect path
      navigate(redirectPath);
    } catch (error) {
      console.error("Registration error:", error);

      switch (error.code) {
        case "auth/email-already-in-use":
          setError("Email is already in use");
          break;
        case "auth/weak-password":
          setError("Password is too weak");
          break;
        case "auth/invalid-email":
          setError("Invalid email address");
          break;
        case "auth/operation-not-allowed":
          setError(
            "Email/password accounts are not enabled. Please contact the administrator."
          );
          break;
        default:
          setError("An error occurred during registration");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const success = await signInWithGoogle();
      if (success) {
        // Get the saved redirect path or default to account page
        const redirectPath = getSavedLocation();
        // Clear the saved path to avoid unexpected redirects in the future
        clearSavedLocation();
        // Navigate to the redirect path
        navigate(redirectPath);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <RegisterContainer>
      <RegisterHeader>
        <RegisterTitle>Create Account</RegisterTitle>
        <p>Sign up to shop our products and track your orders</p>
      </RegisterHeader>

      <RegisterForm onSubmit={handleSubmit}>
        {error && <ErrorMessage>{error}</ErrorMessage>}

        <FormGroup>
          <label htmlFor="name">Full Name</label>
          <Input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
        </FormGroup>

        <FormGroup>
          <label htmlFor="email">Email</label>
          <Input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </FormGroup>

        <FormGroup>
          <label htmlFor="password">Password</label>
          <PasswordInputWrapper>
            <Input
              type={showPassword ? "text" : "password"}
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              $darkMode={darkMode}
            />
            <PasswordToggleButton
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              $darkMode={darkMode}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggleButton>
          </PasswordInputWrapper>
        </FormGroup>

        <FormGroup>
          <label htmlFor="confirmPassword">Confirm Password</label>
          <PasswordInputWrapper>
            <Input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              $darkMode={darkMode}
            />
            <PasswordToggleButton
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              $darkMode={darkMode}
            >
              {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggleButton>
          </PasswordInputWrapper>
        </FormGroup>

        <Button type="submit" disabled={loading} variant="primary" fullWidth>
          {loading ? "Creating Account..." : "Create Account"}
        </Button>
      </RegisterForm>

      <OrDivider>
        <span>OR</span>
      </OrDivider>

      <GoogleButton
        type="button"
        onClick={handleGoogleSignIn}
        disabled={loading}
        fullWidth
        $darkMode={darkMode}
      >
        <FaGoogle style={{ color: "white" }} /> Sign up with Google
      </GoogleButton>

      <LoginLink>
        Already have an account? <Link to="/login">Sign in here</Link>
      </LoginLink>
    </RegisterContainer>
  );
};

export default Register;
