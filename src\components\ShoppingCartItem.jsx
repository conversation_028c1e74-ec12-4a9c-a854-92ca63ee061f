import { motion, Reorder, useMotionValue } from "framer-motion";

const ShoppingCart = ({ items, setItems }) => {
  return (
    <div className="shopping-cart">
      <h2>Your Cart</h2>
      <Reorder.Group axis="y" values={items} onReorder={setItems}>
        {items.map((item) => (
          <CartItem key={item.id} item={item} />
        ))}
      </Reorder.Group>
    </div>
  );
};

const CartItem = ({ item }) => {
  // For custom drag styling
  const y = useMotionValue(0);
  
  return (
    <Reorder.Item value={item} id={item.id} style={{ y }}>
      <div className="cart-item">
        <img src={item.image} alt={item.name} />
        <div className="item-details">
          <h3>{item.name}</h3>
          <p>${item.price.toFixed(2)}</p>
          <div className="quantity-controls">
            {/* quantity controls here */}
          </div>
        </div>
        <div className="drag-handle">
          ⋮⋮ {/* Drag handle indicator */}
        </div>
      </div>
    </Reorder.Item>
  );
};

export default ShoppingCart;