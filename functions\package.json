{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint --no-error-on-unmatched-pattern .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions --force", "logs": "firebase functions:log", "setup-admin": "node ../scripts/setup-admin.mjs"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"@google-cloud/storage": "^7.15.2", "@sendgrid/mail": "^8.1.5", "busboy": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "firebase-admin": "^12.6.0", "firebase-functions": "^6.3.2", "stripe": "^18.1.1"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}