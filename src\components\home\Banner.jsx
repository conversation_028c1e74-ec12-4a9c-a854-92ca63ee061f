import { motion } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography } from '../../styles';
import { useTheme } from '../../context/ThemeContext';

const BannerContainer = styled(motion.div)`
  background: ${props => props.darkMode ? 
    `linear-gradient(135deg, ${colors.primary.dark}, ${colors.secondary.dark})` : 
    `linear-gradient(135deg, ${colors.primary.light}, ${colors.secondary.light})`};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.black};
  padding: ${spacing.xs} ${spacing.md};
  text-align: center;
  overflow: hidden;
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
`;

const TextContainer = styled(motion.div)`
  white-space: nowrap;
  display: flex;
  align-items: center;
`;

const BannerText = styled.span`
  margin: 0;
  font-weight: 500;
  font-size: ${typography.fontSize.sm};
  letter-spacing: 0.5px;
  padding: 0 ${spacing.xl};
`;

const Banner = () => {
  const { darkMode } = useTheme();
  const bannerItems = [
    "🌿 Free shipping on orders over $50",
    "15% off all facial products with code GLOW15",
    "New arrivals: Summer collection now available",
    "Join our rewards program for exclusive perks"
  ];

  return (
    <BannerContainer
      darkMode={darkMode}
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <TextContainer
        animate={{ x: ["0%", "-50%"] }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: 30,
            ease: "linear"
          }
        }}
      >
        {bannerItems.map((item, index) => (
          <BannerText key={index}>
            {item} {index < bannerItems.length - 1 && "•"}
          </BannerText>
        ))}
        {bannerItems.map((item, index) => (
          <BannerText key={`repeat-${index}`}>
            {item} {index < bannerItems.length - 1 && "•"}
          </BannerText>
        ))}
      </TextContainer>
    </BannerContainer>
  );
};

export default Banner;




