import React, { useState, useEffect } from 'react';
import { 
  FaEnvelope, 
  FaEnvelopeOpen, 
  FaStar, 
  FaArchive, 
  FaTrash 
} from 'react-icons/fa';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors } from '../../../styles';import DeleteConfirmationModal from '../../ui/DeleteConfirmationModal';

// Add these styled components for the BatchActions
const BatchActionsContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  background: ${props => props.darkMode ? '#2a2a2a' : '#f5f5f5'};
  padding: 10px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SelectedCount = styled.div`
  margin-right: 16px;
  font-weight: 500;
  color: ${props => props.darkMode ? '#e0e0e0' : '#333'};
`;

const ActionButton = styled(motion.button)`
  background: none;
  border: none;
  color: ${props => {
    // Match colors with the rest of the application
    if (props.action === 'read') return colors.success?.main || '#4CAF50'; // Green for read
    if (props.action === 'unread') return colors.info?.main || '#0088ff'; // Blue for unread
    if (props.action === 'star' || props.action === 'unstar') return colors.warning?.main || '#ffbb00'; // Yellow for star
    if (props.action === 'archive' || props.action === 'unarchive') return '#9c27b0'; // Purple for archive
    if (props.action === 'delete') return '#ff4d4d'; // Red for delete
    return props.darkMode ? '#e0e0e0' : '#555';
  }};
  font-size: 16px;
  padding: 8px;
  margin: 0 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: ${props => {
      if (props.action === 'read' || props.action === 'unread') return `${colors.info?.main || '#0088ff'}20`;
      if (props.action === 'star' || props.action === 'unstar') return `${colors.warning?.main || '#ffbb00'}20`;
      if (props.action === 'archive' || props.action === 'unarchive') return '#9c27b020';
      if (props.action === 'delete') return '#ff4d4d20';
      return props.darkMode ? '#3a3a3a' : '#e8e8e8';
    }};
  }
`;

// Add BatchActions component
export const BatchActions = ({ 
  darkMode, 
  selectedCount, 
  onMarkRead, 
  onMarkUnread, 
  onStar, 
  onUnstar, 
  onArchive, 
  onUnarchive, 
  onDelete,
  selectedMessages,
  messages
}) => {
  const [deleteModal, setDeleteModal] = useState({ isOpen: false });
  
  // Determine if all selected messages are starred/archived
  const determineInitialStates = () => {
    if (!selectedMessages || !messages || selectedMessages.length === 0) {
      return { isStarred: false, isArchived: false };
    }
    
    // Get the selected message objects
    const selectedMessageObjects = messages.filter(msg => 
      selectedMessages.includes(msg.id)
    );
    
    // Check if all selected messages are starred
    const allStarred = selectedMessageObjects.every(msg => msg.isStarred);
    
    // Check if all selected messages are archived
    const allArchived = selectedMessageObjects.every(msg => msg.isArchived);
    
    return { isStarred: allStarred, isArchived: allArchived };
  };
  
  // Initialize states based on selected messages
  const { isStarred: initialStarred, isArchived: initialArchived } = determineInitialStates();
  const [isStarred, setIsStarred] = useState(initialStarred);
  const [isArchived, setIsArchived] = useState(initialArchived);
  
  // Update states when selected messages change
  useEffect(() => {
    const { isStarred: newStarred, isArchived: newArchived } = determineInitialStates();
    setIsStarred(newStarred);
    setIsArchived(newArchived);
  }, [selectedMessages, messages]);

  const handleDelete = () => {
    setDeleteModal({ isOpen: true });
  };

  const confirmDelete = () => {
    onDelete();
    setDeleteModal({ isOpen: false });
  };

  // Toggle functions for star and archive
  const handleStarToggle = () => {
    if (isStarred) {
      onUnstar();
    } else {
      onStar();
    }
    setIsStarred(!isStarred);
  };

  const handleArchiveToggle = () => {
    if (isArchived) {
      onUnarchive();
    } else {
      onArchive();
    }
    setIsArchived(!isArchived);
  };

  return (
    <>
      <BatchActionsContainer 
        darkMode={darkMode}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
      >
        <SelectedCount darkMode={darkMode}>
          {selectedCount} selected
        </SelectedCount>
        
        <ActionButton 
          darkMode={darkMode} 
          onClick={onMarkRead}
          action="read"
          title="Mark as read"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaEnvelopeOpen />
        </ActionButton>
        
        <ActionButton 
          darkMode={darkMode} 
          onClick={onMarkUnread}
          action="unread"
          title="Mark as unread"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaEnvelope />
        </ActionButton>
        
        <ActionButton 
          darkMode={darkMode} 
          onClick={handleStarToggle}
          action={isStarred ? "unstar" : "star"}
          title={isStarred ? "Unstar messages" : "Star messages"}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaStar />
        </ActionButton>
        
        <ActionButton 
          darkMode={darkMode} 
          onClick={handleArchiveToggle}
          action={isArchived ? "unarchive" : "archive"}
          title={isArchived ? "Unarchive messages" : "Archive messages"}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaArchive />
        </ActionButton>
        
        <ActionButton 
          darkMode={darkMode} 
          onClick={handleDelete}
          action="delete"
          title="Delete messages"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaTrash />
        </ActionButton>
      </BatchActionsContainer>

      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false })}
        onConfirm={confirmDelete}
        itemName={`${selectedCount} selected messages`}
        darkMode={darkMode}
      />
    </>
  );
};

const MessageActions = ({ 
  darkMode, 
  message,
  onToggleRead,
  onToggleStar,
  onToggleArchive,
  onDelete
}) => {
  // Component implementation
};

export { MessageActions };
export default MessageActions;








