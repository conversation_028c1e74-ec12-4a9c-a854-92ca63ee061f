import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors, spacing } from '../../../styles';
import { formatReadTimestamp, formatMessageContent, highlightSearchTerms } from './MessageUtils';
import { MessageActions } from './MessageActions';

const DetailContainer = styled(motion.div)`
  padding: ${spacing.md};
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.white};
  
  @media (max-width: 768px) {
    padding: ${spacing.sm};
  }
`;

const MessageSender = styled.div`
  font-size: 14px;
  margin-bottom: ${spacing.sm};
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const MessageBody = styled.div`
  font-size: 14px;
  line-height: 1.6;
  color: ${props => props.darkMode ? colors.neutral.lighter : colors.neutral.darker};
  white-space: pre-wrap;
  margin-bottom: ${spacing.md};
`;

const HighlightStyle = styled.div`
  mark {
    background-color: ${props => props.darkMode ? '#ffcc00' : '#ffff00'};
    color: ${props => props.darkMode ? '#000000' : '#000000'};
    padding: 0 2px;
    border-radius: 2px;
  }
`;

const MessageDetail = ({
  darkMode,
  message,
  onToggleRead,
  onToggleStar,
  onToggleArchive,
  onDelete,
  searchQuery = '' // Add this prop
}) => {
  return (
    <DetailContainer
      darkMode={darkMode}
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <HighlightStyle darkMode={darkMode} />
      <MessageSender 
        darkMode={darkMode}
        dangerouslySetInnerHTML={{ 
          __html: searchQuery ? 
            `From: ${highlightSearchTerms(message.name || 'Unknown', searchQuery)} (${highlightSearchTerms(message.email || 'No email', searchQuery)})` : 
            `From: ${message.name || 'Unknown'} (${message.email || 'No email'})` 
        }}
      />
      
      <MessageBody 
        darkMode={darkMode} 
        expanded={true}
        dangerouslySetInnerHTML={{ 
          __html: formatMessageContent(message.message, searchQuery) 
        }}
      />
      
      <MessageActions 
        darkMode={darkMode}
        message={message}
        onToggleRead={onToggleRead}
        onToggleStar={onToggleStar}
        onToggleArchive={onToggleArchive}
        onDelete={onDelete}
      />
    </DetailContainer>
  );
};

export default MessageDetail;



