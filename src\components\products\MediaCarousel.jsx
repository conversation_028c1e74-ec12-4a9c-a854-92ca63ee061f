import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import {
  FaChevronLeft,
  FaChevronRight,
  FaPlay,
  FaPause,
  FaExpand,
} from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";

const CarouselContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.light};
`;

const MediaWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MediaItem = styled(motion.div)`
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  img,
  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const CarouselControls = styled.div`
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: ${spacing.sm};
  z-index: 10;
`;

const CarouselDots = styled.div`
  display: flex;
  gap: 8px;
`;

const CarouselDot = styled.button`
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: ${(props) => {
    if (props.active && props.isPrimary) {
      return colors.secondary.main; // Special color for primary image
    } else if (props.active) {
      return colors.primary.main;
    } else if (props.isPrimary) {
      return "rgba(255, 255, 255, 0.7)"; // Slightly brighter for primary image
    } else {
      return "rgba(255, 255, 255, 0.5)";
    }
  }};
  border: ${(props) =>
    props.isPrimary ? `1px solid ${colors.secondary.main}` : "none"};
  cursor: pointer;
  transition: background-color 0.3s ease;
  padding: 0;
  ${(props) =>
    props.isPrimary &&
    `
    width: 12px;
    height: 12px;
  `}
`;

const CarouselButton = styled.button`
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.left {
    left: ${spacing.sm};
  }

  &.right {
    right: ${spacing.sm};
  }
`;

const VideoControls = styled.div`
  position: absolute;
  bottom: 15px;
  left: 15px;
  display: flex;
  gap: ${spacing.sm};
  z-index: 10;
`;

const VideoButton = styled.button`
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
`;

const FullscreenButton = styled(VideoButton)`
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
`;

const MediaTypeIndicator = styled.div`
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  font-size: ${typography.fontSize.xs};
  z-index: 10;
`;

const PrimaryIndicator = styled.div`
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: ${colors.secondary.main};
  color: white;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 4px;
  font-size: ${typography.fontSize.xs};
  z-index: 10;
`;

// Helper function to determine media type
const getMediaType = (media) => {
  if (!media || !media.url) return null;

  // If the media object has a type property, use that
  if (media.type) {
    return media.type.toLowerCase();
  }

  // Otherwise try to determine from the URL
  const url = media.url;
  const extension = url.split(".").pop().toLowerCase();

  if (["jpg", "jpeg", "png", "webp"].includes(extension)) {
    return "image";
  } else if (["gif"].includes(extension)) {
    return "gif";
  } else if (["mp4", "webm", "ogg", "mov"].includes(extension)) {
    return "video";
  }

  // Check if URL contains video service indicators
  if (
    url.includes("youtube") ||
    url.includes("vimeo") ||
    (url.includes("cloudinary") && url.includes("video"))
  ) {
    return "video";
  }

  return "image"; // Default to image
};

const MediaCarousel = ({
  mediaItems = [],
  darkMode = false,
  autoPlay = true,
  interval = 5000,
  showControls = true,
  showIndicators = true,
  showMediaType = true,
  onMediaChange = null,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const videoRef = useRef(null);
  const autoPlayTimerRef = useRef(null);

  const currentMedia = mediaItems[activeIndex] || {};
  const mediaType = getMediaType(currentMedia);
  const totalItems = mediaItems.length;

  // Handle auto-play
  useEffect(() => {
    if (autoPlay && isPlaying && mediaType !== "video") {
      autoPlayTimerRef.current = setTimeout(() => {
        goToNext();
      }, interval);
    }

    return () => {
      if (autoPlayTimerRef.current) {
        clearTimeout(autoPlayTimerRef.current);
      }
    };
  }, [activeIndex, autoPlay, isPlaying, mediaType]);

  // Reset video when changing slides
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;

      if (isPlaying && mediaType === "video") {
        // Add a small delay before trying to play to ensure the video is loaded
        const playTimer = setTimeout(() => {
          if (videoRef.current) {
            videoRef.current
              .play()
              .then(() => {
                console.log("Video playing successfully");
              })
              .catch((e) => {
                console.error("Error playing video:", e);
                // If autoplay fails due to browser policy, update UI to show paused state
                setIsPlaying(false);
              });
          }
        }, 100);

        return () => clearTimeout(playTimer);
      }
    }
  }, [activeIndex, isPlaying, mediaType]);

  // Notify parent component when media changes
  useEffect(() => {
    if (onMediaChange) {
      onMediaChange(activeIndex, currentMedia);
    }
  }, [activeIndex, currentMedia, onMediaChange]);

  const goToPrevious = () => {
    setActiveIndex((prev) => (prev === 0 ? totalItems - 1 : prev - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === totalItems - 1 ? 0 : prev + 1));
  };

  const goToSlide = (index) => {
    setActiveIndex(index);
  };

  const togglePlayPause = () => {
    const newPlayingState = !isPlaying;
    setIsPlaying(newPlayingState);

    if (mediaType === "video" && videoRef.current) {
      if (!newPlayingState) {
        videoRef.current.pause();
      } else {
        videoRef.current
          .play()
          .then(() => {
            console.log("Video playing successfully from toggle");
          })
          .catch((e) => {
            console.error("Error playing video from toggle:", e);
            // If play fails, revert the UI state
            setIsPlaying(false);
          });
      }
    }
  };

  const toggleFullscreen = () => {
    if (mediaType === "video" && videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        videoRef.current.requestFullscreen().catch((e) => {
          console.error("Error attempting to enable fullscreen:", e);
        });
      }
    }
  };

  // If no media items, show placeholder
  if (totalItems === 0) {
    return (
      <CarouselContainer darkMode={darkMode}>
        <MediaWrapper>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              color: darkMode ? colors.neutral.light : colors.neutral.dark,
            }}
          >
            No Media Available
          </div>
        </MediaWrapper>
      </CarouselContainer>
    );
  }

  return (
    <CarouselContainer darkMode={darkMode}>
      <MediaWrapper>
        <AnimatePresence mode="wait">
          <MediaItem
            key={activeIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {mediaType === "video" ? (
              <div
                style={{ position: "relative", width: "100%", height: "100%" }}
              >
                <video
                  ref={videoRef}
                  src={currentMedia.url}
                  controls={false}
                  loop
                  muted={currentMedia.muted !== false}
                  playsInline
                  onClick={togglePlayPause}
                  poster={currentMedia.poster || ""}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
                {/* Add a play button overlay that's always visible */}
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    borderRadius: "50%",
                    width: "60px",
                    height: "60px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                    opacity: isPlaying ? 0 : 0.8,
                    transition: "opacity 0.3s ease",
                  }}
                  onClick={togglePlayPause}
                >
                  <FaPlay style={{ color: "white", fontSize: "24px" }} />
                </div>
              </div>
            ) : (
              <img
                src={currentMedia.url}
                alt={currentMedia.alt || `Media ${activeIndex + 1}`}
              />
            )}
          </MediaItem>
        </AnimatePresence>
      </MediaWrapper>

      {showMediaType && mediaType && (
        <MediaTypeIndicator>{mediaType.toUpperCase()}</MediaTypeIndicator>
      )}

      {currentMedia.isPrimary && <PrimaryIndicator>PRIMARY</PrimaryIndicator>}

      {showControls && totalItems > 1 && (
        <>
          <CarouselButton
            className="left"
            onClick={goToPrevious}
            aria-label="Previous"
          >
            <FaChevronLeft />
          </CarouselButton>

          <CarouselButton
            className="right"
            onClick={goToNext}
            aria-label="Next"
          >
            <FaChevronRight />
          </CarouselButton>
        </>
      )}

      {mediaType === "video" && (
        <>
          <VideoControls>
            <VideoButton
              onClick={togglePlayPause}
              aria-label={isPlaying ? "Pause" : "Play"}
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                width: "40px",
                height: "40px",
              }}
            >
              {isPlaying ? (
                <FaPause style={{ fontSize: "18px" }} />
              ) : (
                <FaPlay style={{ fontSize: "18px" }} />
              )}
            </VideoButton>
          </VideoControls>

          <FullscreenButton
            onClick={toggleFullscreen}
            aria-label="Fullscreen"
            style={{
              backgroundColor: "rgba(0, 0, 0, 0.7)",
              width: "40px",
              height: "40px",
            }}
          >
            <FaExpand style={{ fontSize: "18px" }} />
          </FullscreenButton>
        </>
      )}

      {showIndicators && totalItems > 1 && (
        <CarouselControls>
          <CarouselDots>
            {mediaItems.map((item, index) => (
              <CarouselDot
                key={index}
                active={index === activeIndex}
                isPrimary={item.isPrimary}
                onClick={() => goToSlide(index)}
                aria-label={`Go to slide ${index + 1}${
                  item.isPrimary ? " (Primary)" : ""
                }`}
              />
            ))}
          </CarouselDots>
        </CarouselControls>
      )}
    </CarouselContainer>
  );
};

export default MediaCarousel;
