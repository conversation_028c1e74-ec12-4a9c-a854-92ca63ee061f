import { collection, getDocs } from 'firebase/firestore';
import { db } from './config';
import searchClient from '../utils/algoliaClient';

// Function to manually sync Firestore data to Algolia
export const manualAlgoliaSync = async () => {
  try {
    // Get products from Firestore
    const productsSnapshot = await getDocs(collection(db, 'products'));
    const products = productsSnapshot.docs.map(doc => ({
      objectID: doc.id,
      ...doc.data()
    }));
    
    // Get blog posts from Firestore
    const blogPostsSnapshot = await getDocs(collection(db, 'blogPosts'));
    const blogPosts = blogPostsSnapshot.docs.map(doc => ({
      objectID: doc.id,
      ...doc.data()
    }));
    
    // Initialize Algolia indices
    const productsIndex = searchClient.initIndex('products');
    const blogPostsIndex = searchClient.initIndex('blogPosts');
    
    // Save objects to Algolia
    if (products.length > 0) {
      await productsIndex.saveObjects(products);
      console.log(`Synced ${products.length} products to Algolia`);
    }
    
    if (blogPosts.length > 0) {
      await blogPostsIndex.saveObjects(blogPosts);
      console.log(`Synced ${blogPosts.length} blog posts to Algolia`);
    }
    
    return {
      success: true,
      productCount: products.length,
      blogPostCount: blogPosts.length
    };
  } catch (error) {
    console.error("Error manually syncing to Algolia:", error);
    return { error: error.message };
  }
};