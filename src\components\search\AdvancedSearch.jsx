import { InstantSearch, SearchBox, Hits, RefinementList } from 'react-instantsearch';
import searchClient from '../utils/algoliaClient';

const AdvancedSearch = () => {
  return (
    <InstantSearch indexName="products" searchClient={searchClient}>
      <SearchBox placeholder="Search products..." />
      <div className="search-panel">
        <div className="search-panel__filters">
          <RefinementList attribute="category" />
          <RefinementList attribute="tags" />
        </div>
        <div className="search-panel__results">
          <Hits />
        </div>
      </div>
    </InstantSearch>
  );
};

export default AdvancedSearch;




