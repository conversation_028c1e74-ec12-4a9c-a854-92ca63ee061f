import { createContext, useContext, useState, useEffect } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import { getStorageItem, setStorageItem } from '../utils/localStorage';

// Constants
const SEARCH_DEBOUNCE = 300; // ms
const SEARCH_LIMIT = 10; // max results per category

const SearchContext = createContext();

export const useAlgoliaSearch = () => {
  return useContext(SearchContext);
};

export const AlgoliaSearchProvider = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState(() => {
    return getStorageItem('recentSearches') || [];
  });

  // Save recent searches to localStorage
  useEffect(() => {
    setStorageItem('recentSearches', recentSearches);
  }, [recentSearches]);

  // Debounce search to avoid excessive API calls
  useEffect(() => {
    const delaySearch = setTimeout(() => {
      if (searchQuery.trim()) {
        performSearch(searchQuery);
      } else {
        setSearchResults([]);
      }
    }, SEARCH_DEBOUNCE);

    return () => clearTimeout(delaySearch);
  }, [searchQuery]);

  // Perform search across multiple indices
  const performSearch = async (query) => {
    console.log("Starting search with query:", query);
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    try {
      console.log("Using local search method");
      const results = await fallbackSearch(query);
      console.log("Search results:", results);
      setSearchResults(results);
      
      // Add to recent searches if we got results
      if (results.length > 0) {
        addToRecentSearches(query.trim());
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Add a search term to recent searches
  const addToRecentSearches = (term) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(item => item !== term);
      return [term, ...filtered].slice(0, 5);
    });
  };

  // Clear recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  // Toggle search panel
  const toggleSearch = () => {
    setSearchOpen(prev => !prev);
  };

  // Add this function to your AlgoliaSearchContext
  const fallbackSearch = async (query) => {
    console.log("Using fallback search method");
    try {
      // Search products in Firestore
      const productsRef = collection(db, 'products');
      const productsSnapshot = await getDocs(productsRef);
      console.log(`Found ${productsSnapshot.size} products in Firestore`);
      
      // Search blog posts in Firestore
      const blogPostsRef = collection(db, 'blogPosts');
      const blogPostsSnapshot = await getDocs(blogPostsRef);
      console.log(`Found ${blogPostsSnapshot.size} blog posts in Firestore`);
      
      const lowerQuery = query.toLowerCase();
      
      // Filter products client-side
      const filteredProducts = productsSnapshot.docs
        .filter(doc => {
          const data = doc.data();
          return (
            (data.name && data.name.toLowerCase().includes(lowerQuery)) ||
            (data.description && data.description.toLowerCase().includes(lowerQuery)) ||
            (data.category && data.category.toLowerCase().includes(lowerQuery)) ||
            (data.tags && data.tags.some(tag => tag.toLowerCase().includes(lowerQuery)))
          );
        })
        .map(doc => ({
          id: doc.id,
          type: 'product',
          displayName: doc.data().name,
          path: `/shop/product/${doc.id}`,
          image: doc.data().imageUrl || 'https://placehold.co/100x100/png?text=Product',
          price: doc.data().price
        }));
      console.log(`Filtered to ${filteredProducts.length} matching products`);
      
      // Filter blog posts client-side
      const filteredBlogPosts = blogPostsSnapshot.docs
        .filter(doc => {
          const data = doc.data();
          return (
            (data.title && data.title.toLowerCase().includes(lowerQuery)) ||
            (data.excerpt && data.excerpt.toLowerCase().includes(lowerQuery)) ||
            (data.content && data.content.toLowerCase().includes(lowerQuery)) ||
            (data.tags && data.tags.some(tag => tag.toLowerCase().includes(lowerQuery)))
          );
        })
        .map(doc => ({
          id: doc.id,
          type: 'blog',
          displayName: doc.data().title,
          path: `/blog/${doc.id}`,
          date: doc.data().publishDate
        }));
      console.log(`Filtered to ${filteredBlogPosts.length} matching blog posts`);
      
      // Static pages (same as before)
      const pagesConfig = [
        { id: 'home', title: 'Home', path: '/' },
        { id: 'shop', title: 'Shop', path: '/shop' },
        { id: 'contact', title: 'Contact Us', path: '/contact' },
        { id: 'faq', title: 'FAQ', path: '/faq' },
      ];
      
      const filteredPages = pagesConfig
        .filter(page => page.title.toLowerCase().includes(lowerQuery))
        .map(page => ({
          id: page.id,
          type: 'page',
          displayName: page.title,
          path: page.path
        }));
      console.log(`Filtered to ${filteredPages.length} matching pages`);
      
      // Combine all results
      const allResults = [...filteredProducts, ...filteredBlogPosts, ...filteredPages];
      console.log(`Total results: ${allResults.length}`);
      return allResults;
    } catch (error) {
      console.error('Fallback search error:', error);
      return [];
    }
  };

  const value = {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching,
    searchOpen,
    setSearchOpen,
    toggleSearch,
    recentSearches,
    clearRecentSearches
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};













