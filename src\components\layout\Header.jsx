import { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaShoppingCart, FaUser, FaSun, FaMoon, FaBars, FaTimes, FaSearch } from 'react-icons/fa';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { useCart } from '../../context/CartContext';
import { useAlgoliaSearch } from '../../context/AlgoliaSearchContext';
import SearchBar from '../search/SearchBar';

const HeaderContainer = styled.header`
  background-color: ${props => props.darkMode ? colors.neutral.black : colors.neutral.white};
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  box-shadow: ${props => props.darkMode ? 
    '0 2px 4px rgba(255, 255, 255, 0.05)' : 
    '0 2px 4px rgba(0, 0, 0, 0.1)'};
  padding: ${spacing.md} ${spacing.xl};
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: ${props => props.darkMode ? 
    `1px solid ${colors.neutral.dark}` : 
    'none'};
  position: relative;
  flex-wrap: wrap;
`;

const TopRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const SearchRow = styled.div`
  width: 100%;
  padding-top: ${props => props.isOpen ? spacing.md : 0};
  max-height: ${props => props.isOpen ? '100px' : '0'};
  overflow: ${props => props.isOpen ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
  position: relative;
`;

const Logo = styled(Link)`
  font-family: ${typography.fontFamily.secondary};
  font-size: ${typography.fontSize.xl};
  font-weight: ${typography.fontWeight.bold};
  color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
  text-decoration: none;
  
  &:hover {
    text-decoration: none;
  }
`;

const Nav = styled.nav`
  display: flex;
  gap: ${spacing.lg};
  align-items: center;
  
  @media (max-width: ${breakpoints.md}) {
    display: ${props => props.isOpen ? 'flex' : 'none'};
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: ${props => props.darkMode ? colors.neutral.black : colors.neutral.white};
    padding: ${spacing.lg};
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
    align-items: flex-start;
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.xl};
  cursor: pointer;
  
  @media (max-width: ${breakpoints.md}) {
    display: block;
  }
`;

const IconsContainer = styled.div`
  display: flex;
  gap: ${spacing.md};
  align-items: center;
  
  @media (max-width: ${breakpoints.md}) {
    margin-top: ${spacing.md};
    width: 100%;
    justify-content: space-between;
  }
`;

const NavLink = styled(Link)`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-weight: ${typography.fontWeight.medium};
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
    text-decoration: none;
  }
  
  &.active {
    color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
  }
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 1.25rem;
  cursor: pointer;
  padding: ${spacing.xs};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
  
  &:hover {
    color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
  }
`;

const CartIconWrapper = styled(Link)`
  position: relative;
  display: flex;
  align-items: center;
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  text-decoration: none;
  
  &:hover {
    color: ${props => props.darkMode ? colors.primary.light : colors.primary.main};
  }
`;

const CartCount = styled.span`
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: ${colors.primary.main};
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Avatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${colors.primary.light};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const Header = () => {
  const { darkMode, toggleDarkMode } = useTheme();
  const { currentUser } = useAuth();
  const { cartCount } = useCart();
  const { searchOpen, toggleSearch } = useAlgoliaSearch();
  const [menuOpen, setMenuOpen] = useState(false);
  
  
  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };
  
  return (
    <HeaderContainer darkMode={darkMode}>
      <TopRow>
        <Logo to="/" darkMode={darkMode}>Lumina</Logo>
        
        <MobileMenuButton onClick={toggleMenu} darkMode={darkMode}>
          {menuOpen ? <FaTimes /> : <FaBars />}
        </MobileMenuButton>
        
        <Nav isOpen={menuOpen} darkMode={darkMode}>
          <NavLink to="/" darkMode={darkMode} onClick={() => setMenuOpen(false)}>Home</NavLink>
          <NavLink to="/shop" darkMode={darkMode} onClick={() => setMenuOpen(false)}>Shop</NavLink>
          <NavLink to="/blog" darkMode={darkMode} onClick={() => setMenuOpen(false)}>Blog</NavLink>
          
          <IconsContainer>
            <IconButton onClick={toggleSearch} darkMode={darkMode}>
              <FaSearch size={20} />
            </IconButton>
            
            <CartIconWrapper to="/cart" darkMode={darkMode} onClick={() => setMenuOpen(false)}>
              <FaShoppingCart size={20} />
              {cartCount > 0 && <CartCount>{cartCount}</CartCount>}
            </CartIconWrapper>
            
            <IconButton onClick={toggleDarkMode} darkMode={darkMode}>
              {darkMode ? <FaSun size={20} /> : <FaMoon size={20} />}
            </IconButton>
            
            <Link to={currentUser ? "/account" : "/login"} onClick={() => setMenuOpen(false)}>
              <Avatar>
                {currentUser && currentUser.photoURL ? (
                  <img src={currentUser.photoURL} alt="User avatar" />
                ) : (
                  <FaUser color="white" />
                )}
              </Avatar>
            </Link>
          </IconsContainer>
        </Nav>
      </TopRow>
      
      <SearchRow isOpen={searchOpen}>
        <SearchBar />
      </SearchRow>
    </HeaderContainer>
  );
};

export default Header;


