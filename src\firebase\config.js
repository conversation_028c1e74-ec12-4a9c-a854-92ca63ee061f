import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';
import { getAnalytics, isSupported } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();
const storage = getStorage(app);
const functions = getFunctions(app, 'us-central1');

// Initialize Analytics only in browser environment
let analytics = null;

// Initialize analytics asynchronously
const initAnalytics = async () => {
  if (typeof window !== 'undefined' && await isSupported()) {
    analytics = getAnalytics(app);
    console.log('Firebase Analytics initialized');
  }
};

// Call the initialization function
initAnalytics().catch(error => {
  console.error('Failed to initialize analytics:', error);
});

// Helper function for exponential backoff
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Add a timestamp-based cache to avoid excessive refreshes
let lastTokenRefresh = 0;
const TOKEN_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Improve the refreshTokenWithBackoff function
export const refreshTokenWithBackoff = async (user, maxRetries = 5) => {
  // Check if we've refreshed recently
  const now = Date.now();
  if (now - lastTokenRefresh < TOKEN_REFRESH_INTERVAL) {
    console.log('Token was refreshed recently, skipping refresh');
    return true;
  }
  
  let retries = 0;
  let lastError = null;

  while (retries < maxRetries) {
    try {
      await user.getIdToken(true);
      lastTokenRefresh = Date.now(); // Update timestamp
      return true;
    } catch (error) {
      lastError = error;
      console.log(`Token refresh attempt ${retries + 1} failed:`, error.code);
      
      if (error.code === 'auth/quota-exceeded') {
        // For quota errors, consider it a "soft success" if we already have a token
        if (user.accessToken) {
          console.log('Using existing token due to quota limits');
          return true;
        }
        
        // Wait with exponential backoff: 1s, 2s, 4s, 8s, 16s
        const waitTime = Math.pow(2, retries) * 1000;
        console.log(`Waiting ${waitTime}ms before retry...`);
        await wait(waitTime);
        retries++;
      } else {
        // For other errors, don't retry
        break;
      }
    }
  }
  
  console.error('Token refresh failed after retries:', lastError);
  return false;
};

export { app, db, auth, googleProvider, storage, functions, analytics };

// Add this to your firebase config file
export const checkAuth = async () => {
  const user = auth.currentUser;
  if (user) {
    // Force token refresh
    await user.getIdToken(true);
    const token = await user.getIdTokenResult();
    console.log("User authenticated:", user.email);
    console.log("Admin claim:", token.claims.admin);
    return token.claims;
  }
  return null;
};
